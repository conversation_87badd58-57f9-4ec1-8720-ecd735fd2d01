{"name": "optimize", "version": "0.0.0", "workspaces": ["apps/*", "libs/*", "tools/executors/*"], "license": "MIT", "scripts": {"configure-optimize": "chmod +x ./optimize-env.sh && ./optimize-env.sh && cp apps/Optimize/env-config.js apps/Optimize/src", "configure-keystone": "chmod +x ./keystone-env.sh && ./keystone-env.sh && cp apps/Keystone/env-config.js apps/Keystone/src", "configure-temp-card": "chmod +x ./temp-card-env.sh && ./temp-card-env.sh && cp apps/TempCard/env-config.js apps/TempCard/src", "configure-admin-portal": "chmod +x ./admin-portal-env.sh && ./admin-portal-env.sh && cp apps/AdminPortal/env-config.js apps/AdminPortal/src", "whereAmI": "ls", "packages:workspaces": "(yarn)", "start": "yarn configure-optimize && nx serve Optimize", "start:evaluate": "yarn configure-optimize && NX_MODULE=evaluate nx serve Optimize", "start:file": "yarn configure-optimize && NX_MODULE=file nx serve Optimize", "start:admin": "yarn configure-optimize && NX_MODULE=admin nx serve Optimize", "start:agent": "yarn configure-optimize && NX_MODULE=agent nx serve Optimize", "start:members": "yarn configure-optimize && NX_MODULE=members nx serve Optimize", "start:organization": "yarn configure-optimize && NX_MODULE=organization nx serve Optimize", "start:keystone": "yarn configure-keystone && nx serve Keystone", "start:tempCard": "yarn configure-temp-card && nx serve TempCard", "start:adminPortal": "(cd next && nx serve admin-portal)", "start:memberPortal": "export NX_DAEMON=false; cd next && nx serve member-portal", "start:adminPortalQlik": "(cd next && nx serve admin-portal) && yarn run:mkcert", "serveProd:adminPortal": "(cd next && nx run admin-portal:serve:production)", "start:NoNxAdmin": "export NX_DAEMON=false; yarn start:adminPortal", "test": "cypress open --project ./", "build": "node_modules/.bin/nx build Optimize --prod", "build:keystone": "node_modules/.bin/nx build Keystone --prod", "build:adminPortal": "(cd next && nx build admin-portal --verbose)", "build:apps": "node_modules/.bin/nx run-many --target=build -p Keystone Optimize TempCard --configuration=production --all --parallel", "build:next": "(cd next && npx nx run-many --target=build -p admin-portal member-portal --configuration=production --all --verbose)", "build:nextDocker": "(cd next && yarn && npx nx run-many --target=build -p admin-portal --configuration=production --all --parallel)", "build:all": "yarn build:apps && yarn build:next", "build:next:affected": "cd next && npx nx affected:build --base=origin/feature/NextDev~1 --head=origin/feature/NextDev  -p admin-portal member-portal --configuration=production --all --parallel", "affected": "nx affected:graph", "deps": "nx graph", "format": "nx format:write --uncommitted", "lint": "nx affected:lint --max-warnings=0 --uncommitted", "lintFix": "yarn nx affected:lint --fix=true", "prepare": "husky install", "start:docs": "(cd next && nx storybook rxb-ui)", "build:docs": "(cd next && npx nx build-storybook rxb-ui)", "publish:docs": "gh-pages -d ./next/libs/rxb-ui/storybook-static -b gh-pages", "openapi:evaluate": "npx openapi-typescript https://evaluate-service-qa.rxbenefits.cloud/docs/openapi.json --output libs/api/src/lib/Evaluate/ProspectOpps/prospectOppsService.schema.ts", "openapi:data-mapper": "npx openapi-typescript https://data-mapping-service-qa.rxbenefits.cloud/datamapping/v1/docs/swagger.json --output libs/api/src/lib/DataMapper/Templates/templatesService.schema.ts", "openapi:get-by-branch": "sh -c 'gh api https://api.github.com/repos/RxBenefits/evaluate-service/contents/api/openapi.json --field ref=$0 --method GET --header Accept:application/vnd.github.VERSION.raw > openApiByBranch.json && yarn openapi:build-by-branch'", "openapi:build-by-branch": "npx openapi-typescript openApiByBranch.json --output libs/api/src/lib/Evaluate/ProspectOpps/prospectOppsService.schema.ts && rm -rf openApiByBranch.json", "UpdateCA": "npm config set cafile ../../Documents/Zscaler.pem && npm config get cafile"}, "private": true, "dependencies": {"@ant-design/icons": "4.7.0", "@auth0/auth0-react": "^2.2.4", "@datadog/browser-rum": "^4.41.0", "@fontsource/roboto": "^5.0.8", "@nx/devkit": "^20.6.4", "@nx/eslint": "^20.6.4", "@nx/next": "^20.6.4", "@storybook/addon-interactions": "^8.2.8", "@storybook/addon-postcss": "^2.0.0", "@types/formidable": "^3.4.0", "antd": "4.18.7", "array-move": "^4.0.0", "axios": "0.24.0", "core-js": "3.36.1", "eslint-plugin-storybook": "^0.6.12", "exceljs": "^4.4.0", "formidable": "^3.5.0", "lodash": "^4.17.21", "mysql": "^2.18.1", "node-fetch": "^3.3.2", "postcss": "8.4.38", "postcss-loader": "^6.2.1", "react": "18.3.1", "react-custom-scrollbars": "^4.2.1", "react-dom": "18.3.1", "react-highlight-words": "^0.18.0", "react-input-mask": "2.0.4", "react-joyride": "2.9.3", "react-query": "3.31.0", "react-router-dom": "6.11.2", "react-signature-canvas": "^1.0.6", "react-sortable-hoc": "^2.0.0", "react-virtualized": "^9.22.5", "regenerator-runtime": "0.13.7", "storybook": "^8.2.8", "styled-components": "5.3.11", "throttle-debounce": "^3.0.1", "tslib": "2.0.0", "uid-safe": "^2.1.5", "web-vitals": "2.1.0", "zod": "^3.24.2"}, "devDependencies": {"@babel/core": "7.12.13", "@babel/preset-typescript": "7.12.13", "@nrwl/js": "19.6.0", "@nrwl/linter": "19.6.0", "@nrwl/next": "^19.8.4", "@nrwl/workspace": "^19.8.4", "@nx/cypress": "19.6.0", "@nx/eslint-plugin": "^20.6.4", "@nx/jest": "^20.6.4", "@nx/js": "^20.6.4", "@nx/react": "^20.6.4", "@nx/storybook": "^20.6.3", "@nx/vite": "^20.6.3", "@nx/web": "19.6.0", "@nx/webpack": "19.6.0", "@nx/workspace": "^20.6.4", "@storybook/addon-essentials": "8.2.9", "@storybook/builder-webpack5": "6.5.9", "@storybook/manager-webpack5": "6.5.9", "@storybook/react": "8.2.9", "@svgr/webpack": "8.1.0", "@testing-library/react": "15.0.6", "@testing-library/react-hooks": "7.0.2", "@types/lodash.debounce": "^4.0.9", "@types/react": "18.3.1", "@types/react-dom": "18.3.0", "@types/react-highlight-words": "^0.16.4", "@typescript-eslint/eslint-plugin": "7.18.0", "@typescript-eslint/parser": "7.18.0", "babel-eslint": "10.1.0", "babel-loader": "8.1.0", "cypress-file-upload": "5.0.8", "cypress-grep": "^2.13.1", "cypress-localstorage-commands": "^1.7.0", "cypress-plugin-tab": "^1.0.5", "eslint": "8.57.0", "eslint-config-prettier": "9.1.0", "eslint-config-react-app": "7.0.0", "eslint-plugin-cypress": "2.15.2", "eslint-plugin-import": "2.25.2", "eslint-plugin-jsx-a11y": "6.4.1", "eslint-plugin-react": "7.26.1", "eslint-plugin-react-hooks": "4.3.0", "eslint-plugin-simple-import-sort": "7.0.0", "gh-pages": "3.1.0", "husky": "7.0.1", "lint-staged": "12.1.7", "nx": "19.6.0", "nx-cloud": "19.0.0", "prettier": "2.5.1", "react-test-renderer": "17.0.2", "typescript": "4.3.5", "url-loader": "3.0.0"}, "resolutions": {"@types/react": "18.3.12"}, "lint-staged": {"*": ["yarn lint", "yarn format"]}, "packageManager": "yarn@1.22.19+sha1.4ba7fc5c6e704fce2066ecbfb0b0d8976fe62447"}