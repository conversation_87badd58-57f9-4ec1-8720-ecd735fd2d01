#!/bin/bash
# Users can use this script to easily deploy entire projects or cherry pick
# PR's to get into the demo branch. Once changes have been merged to the demo branch you will
# need to grab the octopus build id from the PR's build and use it to deploy to demo

# Trap function to clean up on any exit
cleanup_on_exit() {
  clean_state
}

# Set up trap to catch exits
trap cleanup_on_exit EXIT

# Color definitions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Helper functions for printing colored text
print_header() {
  echo -e "\n${BLUE}${BOLD}==== $1 ====${NC}\n"
}

print_success() {
  echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
  echo -e "${RED}✗ ERROR: $1${NC}"
}

print_warning() {
  echo -e "${YELLOW}⚠ $1${NC}"
}

print_info() {
  echo -e "${CYAN}ℹ $1${NC}"
}

print_step() {
  echo -e "\n${BOLD}$1${NC}"
}

# Clean up state files
clean_state() {
  rm -f .demoDeploy_pr_list .demoDeploy_current_pr .demoDeploy_commits .demoDeploy_current_commit
  
  # Clean up any other temporary files that might be created
  rm -f .demoDeploy_* # Remove any other temporary files with this prefix
  
  print_info "Cleaned up all temporary files"
}

# Helper function to check if a commit is a merge commit
is_merge_commit() {
  local COMMIT=$1
  local PARENT_COUNT=$(git cat-file -p $COMMIT | grep -c "^parent ")
  
  if [ $PARENT_COUNT -gt 1 ]; then
    return 0  # true
  else
    return 1  # false
  fi
}

# Check for in-progress operations
check_in_progress_operations() {
  if [ -d ".git/CHERRY_PICK_HEAD" ] || [ -d ".git/rebase-apply" ] || [ -d ".git/rebase-merge" ]; then
    print_warning "Detected a cherry-pick or rebase operation in progress."
    read -p "$(echo -e ${YELLOW})Would you like to continue the in-progress operation? (y/n): $(echo -e ${NC})" CONTINUE_OP
    
    if [[ $CONTINUE_OP =~ ^[Yy]$ ]]; then
      git cherry-pick --continue
      
      if [ $? -eq 0 ]; then
        print_success "Cherry-pick continued successfully."
        # Get the current branch to continue the script
        CURRENT_BRANCH=$(git branch --show-current)
        NEW_VERSION=$CURRENT_BRANCH
        
        # Jump to the cherry-pick workflow continuation
        return 0 # true - continue with cherry-pick
      else
        print_error "Failed to continue cherry-pick. Please resolve conflicts and try again."
        exit 1
      fi
    else
      print_info "Aborting the in-progress operation."
      git cherry-pick --abort 2>/dev/null || git rebase --abort 2>/dev/null
      print_info "Operation aborted. Continuing with normal script execution."
      return 1 # false - continue with normal execution
    fi
  else
    return 1 # false - continue with normal execution
  fi
}

# Check for uncommitted changes
check_uncommitted_changes() {
  if [ -n "$(git status --porcelain)" ]; then
    print_error "You have uncommitted changes in your working directory."
    print_info "Please commit or stash your changes before running this script."
    exit 1
  fi
}

# Setup branch for deployment
setup_branch() {
  # Check current branch and switch to feature/demo if necessary
  CURRENT_BRANCH=$(git branch --show-current)
  if [ "$CURRENT_BRANCH" != "feature/demo" ]; then
    print_info "You are not on feature/demo branch. Switching to feature/demo..."
    git checkout feature/demo
    
    # Verify the switch was successful
    if [ $? -ne 0 ]; then
      print_error "Failed to switch to feature/demo branch. Does it exist?"
      exit 1
    else
      print_success "Successfully switched to feature/demo branch."
    fi
  fi

  # Find the latest rc/0.1.x branch
  LATEST_RC=$(git branch | grep -E "rc/0\.1\.[0-9]+" | sed 's/^[* ]*//g' | sort -V | tail -n 1)

  if [ -z "$LATEST_RC" ]; then
    # No existing rc/0.1.x branch, start with rc/0.1.0
    print_info "No existing rc/0.1.x branch found. Creating rc/0.1.0"
    NEW_VERSION="rc/0.1.0"
    
    # Ask user if they want to continue with creation
    read -p "$(echo -e ${YELLOW})Continue with creating new branch $NEW_VERSION? (y/n): $(echo -e ${NC})" CONTINUE
    if [[ ! $CONTINUE =~ ^[Yy]$ ]]; then
      print_info "Branch creation cancelled."
      exit 0
    fi
    
    # Create the new branch
    print_step "Creating new branch: $NEW_VERSION"
    git checkout -b $NEW_VERSION
    print_success "Successfully created branch: $NEW_VERSION"
  else
    # Found an existing RC branch
    print_info "Found existing branch $LATEST_RC."
    print_info "OPTIONS:"
    echo -e "  ${CYAN}1. Use the existing branch${NC}"
    echo -e "  ${CYAN}2. Delete it and create a new branch${NC}"
    echo -e "  ${CYAN}3. Keep it and create a new branch anyway${NC}"
    read -p "$(echo -e ${YELLOW})Choose an option (1/2/3): $(echo -e ${NC})" BRANCH_OPTION
    
    case $BRANCH_OPTION in
      1)
        # Use the existing branch
        print_info "Using existing branch: $LATEST_RC"
        git checkout $LATEST_RC
        
        if [ $? -ne 0 ]; then
          print_error "Failed to switch to $LATEST_RC branch."
          exit 1
        fi
        NEW_VERSION=$LATEST_RC
        print_success "Successfully switched to branch: $NEW_VERSION"
        ;;
      2)
        # Delete existing and create new
        # Extract the version number, this gives us the "x" from "rc/0.1.x"
        VERSION=$(echo $LATEST_RC | sed -E 's/.*rc\/0\.1\.([0-9]+)/\1/')
        
        # Increment the version
        if [ "$VERSION" = "9" ]; then
          # If version is 9, increment to 0.2.0
          NEW_VERSION="rc/0.2.0"
        else
          # Otherwise, just increment the last digit
          NEW_VERSION="rc/0.1.$((VERSION + 1))"
        fi
        
        print_info "Deleting branch: $LATEST_RC"
        git branch -D $LATEST_RC
        
        # Ask user if they want to continue with creation
        read -p "$(echo -e ${YELLOW})Continue with creating new branch $NEW_VERSION? (y/n): $(echo -e ${NC})" CONTINUE
        if [[ ! $CONTINUE =~ ^[Yy]$ ]]; then
          print_info "Branch creation cancelled."
          exit 0
        fi
        
        # Create the new branch
        print_step "Creating new branch: $NEW_VERSION"
        git checkout -b $NEW_VERSION
        print_success "Successfully created branch: $NEW_VERSION"
        ;;
      3)
        # Keep existing but create new
        # Extract the version number, this gives us the "x" from "rc/0.1.x"
        VERSION=$(echo $LATEST_RC | sed -E 's/.*rc\/0\.1\.([0-9]+)/\1/')
        
        # Increment the version
        if [ "$VERSION" = "9" ]; then
          # If version is 9, increment to 0.2.0
          NEW_VERSION="rc/0.2.0"
        else
          # Otherwise, just increment the last digit
          NEW_VERSION="rc/0.1.$((VERSION + 1))"
        fi
        
        # Ask user if they want to continue with creation
        read -p "$(echo -e ${YELLOW})Continue with creating new branch $NEW_VERSION? (y/n): $(echo -e ${NC})" CONTINUE
        if [[ ! $CONTINUE =~ ^[Yy]$ ]]; then
          print_info "Branch creation cancelled."
          exit 0
        fi
        
        # Create the new branch
        print_step "Creating new branch: $NEW_VERSION"
        git checkout -b $NEW_VERSION
        print_success "Successfully created branch: $NEW_VERSION"
        ;;
      *)
        print_error "Invalid option selected. Exiting."
        exit 1
        ;;
    esac
  fi
}

# Function to handle cherry-picking for a single PR
cherry_pick_pr() {
  local PR_NUMBER=$1
  
  print_step "Processing PR #$PR_NUMBER..."
  
  # Get PR data - look for commits directly in the PR number
  PR_COMMITS=$(git log --all --grep="Merge pull request #$PR_NUMBER" --format="%H")
  
  # If not found by that method, look for the PR number in commit messages
  if [ -z "$PR_COMMITS" ]; then
    print_info "Looking for PR in commit messages..."
    # Try different patterns that might appear in commit messages
    PR_COMMITS=$(git log --all --grep="#$PR_NUMBER" --format="%H")
    
    # Additional search patterns if needed
    if [ -z "$PR_COMMITS" ]; then
      PR_COMMITS=$(git log --all --grep="PR $PR_NUMBER" --format="%H")
    fi
    
    if [ -z "$PR_COMMITS" ]; then
      PR_COMMITS=$(git log --all --grep="PR#$PR_NUMBER" --format="%H")
    fi
  fi
  
  # If still not found, look for it in the full git log
  if [ -z "$PR_COMMITS" ]; then
    print_info "No commits found directly for PR #$PR_NUMBER. Looking for related commits..."
    
    # Try to find commits that might be associated with this PR
    PR_BRANCH_COMMITS=$(git log --all --grep="feature/.*$PR_NUMBER" --format="%H")
    if [ -n "$PR_BRANCH_COMMITS" ]; then
      PR_COMMITS=$PR_BRANCH_COMMITS
    fi
  fi
  
  # If still no commits found, ask for them manually
  if [ -z "$PR_COMMITS" ]; then
    print_warning "No commits found automatically for PR #$PR_NUMBER."
    
    # Show relevant info from the git log to help user identify commits
    print_info "Recent commits (last 10):"
    git log -n 10 --oneline
    
    echo -e "${YELLOW}Please enter the commit hash(es) for PR #$PR_NUMBER (space-separated if multiple):${NC}"
    read PR_COMMITS
  else
    print_success "Found these commits for PR #$PR_NUMBER:"
    for COMMIT in $PR_COMMITS; do
      git show --oneline -s $COMMIT
    done
    read -p "$(echo -e ${YELLOW})Do you want to cherry-pick these commits? (y/n): $(echo -e ${NC})" CONFIRM_COMMITS
    if [[ ! $CONFIRM_COMMITS =~ ^[Yy]$ ]]; then
      echo -e "${YELLOW}Please enter the commit hash(es) for PR #$PR_NUMBER (space-separated if multiple):${NC}"
      read PR_COMMITS
    else
      # Ask if they want to omit any specific commits
      read -p "$(echo -e ${YELLOW})Would you like to omit any specific commits? (y/n): $(echo -e ${NC})" OMIT_COMMITS
      
      if [[ $OMIT_COMMITS =~ ^[Yy]$ ]]; then
        while true; do
          echo -e "${YELLOW}Enter the commit hash(es) you want to OMIT (space-separated, or press Enter for none):${NC}"
          read COMMITS_TO_OMIT
          
          if [ -z "$COMMITS_TO_OMIT" ]; then
            # No commits to omit
            read -p "$(echo -e ${YELLOW})Continue with all commits? (y/n): $(echo -e ${NC})" CONTINUE_ALL
            if [[ $CONTINUE_ALL =~ ^[Yy]$ ]]; then
              break
            fi
          else
            # Filter out the commits to omit
            FILTERED_COMMITS=""
            for COMMIT in $PR_COMMITS; do
              if ! echo "$COMMITS_TO_OMIT" | grep -q "$COMMIT"; then
                FILTERED_COMMITS="$FILTERED_COMMITS $COMMIT"
              fi
            done
            FILTERED_COMMITS=$(echo "$FILTERED_COMMITS" | xargs) # Trim whitespace
            
            if [ -z "$FILTERED_COMMITS" ]; then
              print_warning "All commits would be omitted. Please select commits to keep or none to omit."
            else
              print_info "The following commits will be cherry-picked:"
              for COMMIT in $FILTERED_COMMITS; do
                git show --oneline -s $COMMIT
              done
              
              read -p "$(echo -e ${YELLOW})Proceed with these commits? (y/n): $(echo -e ${NC})" PROCEED_FILTERED
              if [[ $PROCEED_FILTERED =~ ^[Yy]$ ]]; then
                PR_COMMITS=$FILTERED_COMMITS
                break
              fi
            fi
          fi
        done
      fi
    fi
  fi
  
  # Now cherry-pick each commit
  for COMMIT in $PR_COMMITS; do
    print_step "Cherry-picking commit: $COMMIT"
    
    # Save state before attempting the cherry-pick
    echo "$PR_COMMITS" > .demoDeploy_commits
    echo "$COMMIT" > .demoDeploy_current_commit
    
    # Check if the commit is a merge commit
    if is_merge_commit "$COMMIT"; then
      print_info "Detected merge commit. Using -m 1 option..."
      git cherry-pick -m 1 $COMMIT
    else
      git cherry-pick $COMMIT
    fi
    
    # Check if cherry-pick was successful
    if [ $? -ne 0 ]; then
      print_warning "Cherry-pick of $COMMIT failed. There might be conflicts."
      print_info "OPTIONS:"
      echo -e "  ${CYAN}1. Resolve conflicts now manually in another terminal${NC}"
      echo -e "  ${CYAN}2. Exit the script, resolve conflicts, then run 'git cherry-pick --continue', then rerun this script${NC}"
      echo -e "  ${CYAN}3. Abort the cherry-pick and continue with the next PR${NC}"
      read -p "$(echo -e ${YELLOW})Choose an option (1/2/3): $(echo -e ${NC})" RESOLVE_OPTION
      
      case $RESOLVE_OPTION in
        1)
          # Loop until cherry-pick is successful or user exits
          while true; do
            print_info "Please resolve conflicts in another terminal window."
            print_info "When finished, come back here and press Enter to continue."
            read -p "$(echo -e ${YELLOW})Press Enter after resolving conflicts... $(echo -e ${NC})" 
            
            # Try cherry-pick continue
            git cherry-pick --continue
            
            # Check if cherry-pick was successful
            if [ $? -eq 0 ]; then
              print_success "Successfully continued cherry-pick"
              break
            else
              # Cherry-pick failed - could be linting or other issues
              if git status | grep -q "cherry-pick in progress"; then
                # Cherry-pick is still in progress - likely a hook/linting failure
                print_warning "Cherry-pick is still in progress. This is likely due to linting or pre-commit hook failures."
                print_info "OPTIONS:"
                echo -e "  ${CYAN}1. Fix linting/hook issues and try again${NC}"
                echo -e "  ${CYAN}2. Skip linting/hooks and force continue (--no-verify)${NC}"
                echo -e "  ${CYAN}3. Exit script to handle manually${NC}"
                read -p "$(echo -e ${YELLOW})Choose an option (1/2/3): $(echo -e ${NC})" LINT_OPTION
                
                case $LINT_OPTION in
                  1)
                    print_info "Please fix the issues in another terminal."
                    print_info "Be sure to stage your changes with: git add <files>"
                    # Loop continues to allow another attempt
                    ;;
                  2)
                    print_warning "Bypassing hooks with --no-verify"
                    git cherry-pick --continue --no-verify
                    if [ $? -eq 0 ]; then
                      print_success "Cherry-pick completed (hooks bypassed)"
                      break
                    else
                      print_error "Cherry-pick still failed even with --no-verify"
                      exit 1
                    fi
                    ;;
                  3)
                    print_info "Exiting script. After fixing issues:"
                    print_info "1. Stage your changes with 'git add'"
                    print_info "2. Run 'git cherry-pick --continue'"
                    print_info "3. Re-run this script if needed"
                    exit 1
                    ;;
                  *)
                    print_warning "Invalid option. Please try again."
                    ;;
                esac
              else
                # Not a cherry-pick in progress - some other error
                print_error "Unexpected error during cherry-pick. Please fix manually."
                exit 1
              fi
            fi
          done
          ;;
        2)
          print_info "Exiting script. After resolving conflicts:"
          print_info "1. Run 'git cherry-pick --continue'"
          print_info "2. Then re-run this script to continue the process"
          print_info "The script will detect the in-progress cherry-pick and resume."
          exit 1
          ;;
        3)
          print_info "Aborting cherry-pick and continuing with next PR."
          git cherry-pick --abort
          rm -f .demoDeploy_commits .demoDeploy_current_commit
          return 1
          ;;
      esac
    else
      print_success "Successfully cherry-picked $COMMIT"
    fi
  done
  
  return 0
}

# Function to merge project from develop
merge_from_develop() {
  local PROJECT=$1
  
  print_header "Merging from develop branch"
  
  # First make sure develop branch exists and is up to date
  if ! git show-ref --verify --quiet refs/heads/develop; then
    print_error "develop branch does not exist."
    return 1
  fi
  
  # Ensure develop branch is up to date
  print_step "Fetching latest changes..."
  git fetch origin develop
  
  case "$PROJECT" in
    "admin-portal")
      print_step "Merging admin-portal from develop branch..."
      git checkout develop -- next/apps/admin-portal
      ;;
    "member-portal")
      print_step "Merging member-portal from develop branch..."
      git checkout develop -- next/apps/member-portal
      ;;
    "all")
      print_step "Merging all changes from develop branch..."
      git merge --no-commit develop
      ;;
    *)
      print_error "Invalid project selection."
      return 1
      ;;
  esac
  
  # Check if the merge resulted in staged changes
  if [ -n "$(git diff --cached)" ]; then
    print_success "Changes have been staged successfully."
    print_info "Reviewing changes..."
    git status
    
    read -p "$(echo -e ${YELLOW})Would you like to commit these changes? (y/n): $(echo -e ${NC})" COMMIT_CONFIRM
    if [[ $COMMIT_CONFIRM =~ ^[Yy]$ ]]; then
      read -p "$(echo -e ${YELLOW})Enter a commit message: $(echo -e ${NC})" COMMIT_MSG
      git commit -m "$COMMIT_MSG"
      print_success "Changes committed successfully."
    else
      print_info "Changes remain staged but not committed."
    fi
  else
    print_warning "No changes were staged."
  fi
  
  return 0
}

# Function to handle cherry-pick workflow
cherry_pick_workflow() {
  print_header "Cherry-Pick Mode"
  print_info "You can enter multiple PR numbers separated by spaces (e.g., '4750 4752 4760')"
  read -p "$(echo -e ${YELLOW})Enter PR number(s): $(echo -e ${NC})" PR_NUMBERS
  
  # Save state for resumption
  echo "$PR_NUMBERS" > .demoDeploy_pr_list
  
  # Process each PR number
  for PR_NUMBER in $PR_NUMBERS; do
    echo "$PR_NUMBER" > .demoDeploy_current_pr
    if ! cherry_pick_pr "$PR_NUMBER"; then
      print_warning "Failed to process PR #$PR_NUMBER. Continuing with next PR."
    fi
  done
  
  # Clean up state files 
  clean_state
  
  # Ask if they want to add more PRs
  while true; do
    read -p "$(echo -e ${YELLOW})Would you like to cherry-pick more PRs? (y/n): $(echo -e ${NC})" MORE_PRS
    if [[ ! $MORE_PRS =~ ^[Yy]$ ]]; then
      break
    fi
    
    print_info "Enter additional PR numbers separated by spaces:"
    read -p "$(echo -e ${YELLOW})PR numbers: $(echo -e ${NC})" PR_NUMBERS
    
    # Save state for resumption
    echo "$PR_NUMBERS" > .demoDeploy_pr_list
    
    # Process each additional PR number
    for PR_NUMBER in $PR_NUMBERS; do
      echo "$PR_NUMBER" > .demoDeploy_current_pr
      if ! cherry_pick_pr "$PR_NUMBER"; then
        print_warning "Failed to process PR #$PR_NUMBER. Continuing with next PR."
      fi
    done
    
    # Clean up state files
    clean_state
  done
}

# Resume cherry-pick session if interrupted
resume_cherry_pick_session() {
  print_header "Resuming interrupted cherry-pick session"
  REMAINING_PRS=$(cat .demoDeploy_pr_list)
  CURRENT_PR=$(cat .demoDeploy_current_pr 2>/dev/null || echo "")
  
  if [ -n "$CURRENT_PR" ]; then
    print_info "Continuing with PR #$CURRENT_PR..."
    if ! cherry_pick_pr "$CURRENT_PR"; then
      print_warning "Failed to complete PR #$CURRENT_PR."
    fi
  fi
  
  # Process remaining PRs
  for PR in $REMAINING_PRS; do
    if [ "$PR" != "$CURRENT_PR" ]; then
      echo "$PR" > .demoDeploy_current_pr
      if ! cherry_pick_pr "$PR"; then
        print_warning "Failed to process PR #$PR. Continuing with next PR."
      fi
    fi
  done
  
  # Clean up state files
  clean_state
}

# Display menu and handle user choice
show_menu_and_handle_choice() {
  print_header "Deployment Options"
  echo -e "  ${CYAN}1. Cherry-pick commits from specific PRs${NC}"
  echo -e "  ${CYAN}2. Merge admin-portal from develop branch${NC}"
  echo -e "  ${CYAN}3. Merge member-portal from develop branch${NC}"
  echo -e "  ${CYAN}4. Merge all changes from develop branch${NC}"
  echo -e "  ${CYAN}5. Exit without any changes${NC}"
  read -p "$(echo -e ${YELLOW})Choose an option (1-5): $(echo -e ${NC})" DEPLOY_OPTION
  
  case $DEPLOY_OPTION in
    1) # Cherry-pick PRs
      cherry_pick_workflow
      ;;
    2) # Merge admin-portal
      merge_from_develop "admin-portal"
      ;;
    3) # Merge member-portal
      merge_from_develop "member-portal"
      ;;
    4) # Merge all from develop
      merge_from_develop "all"
      ;;
    5) # Exit
      print_info "Exiting without making changes."
      exit 0
      ;;
    *)
      print_error "Invalid option selected. Exiting."
      exit 1
      ;;
  esac
}

# Main script execution
print_header "Demo Deployment Script"

# Check for in-progress operations
if check_in_progress_operations; then
  # Continue with cherry-pick workflow
  goto_cherry_pick_continuation=true
else
  goto_cherry_pick_continuation=false
fi

if [ "$goto_cherry_pick_continuation" != "true" ]; then
  # Normal script execution
  check_uncommitted_changes
  setup_branch
fi

# Check if we should resume a previously interrupted cherry-pick session
if [ -f ".demoDeploy_pr_list" ] || [ "$goto_cherry_pick_continuation" = "true" ]; then
  if [ -f ".demoDeploy_pr_list" ]; then
    resume_cherry_pick_session
  fi
else
  # Show menu and handle user choice
  show_menu_and_handle_choice
fi

# Script will auto-clean on exit due to trap
print_success "Script completed successfully!"
print_info "You are now on branch: ${BOLD}$NEW_VERSION${NC}"