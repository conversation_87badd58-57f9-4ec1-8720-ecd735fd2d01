# See http://help.github.com/ignore-files/ for more about ignoring files.

# compiled output
/dist
/tmp
/out-tsc

# dependencies
/node_modules
package-lock.json

# testing
/coverage

# Cypress
cypress.env.json
/cypress/videos
/cypress/screenshots
/cypress/downloads

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Snyk
.dccache

# environment specific config overrides
.*-env.local

# misc
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
yarn-error.log
testem.log
/typings

# System Files
.DS_Store
Thumbs.db

# ESLint cache
.eslintcache

# generated env files
/apps/Optimize/env-config.js
/apps/Keystone/env-config.js

# Snyk
.dccache

# Next.js
.next
.nx

# Nx
.nx

# Nx
.nx

localhost-key.pem
localhost.pem

# Demo deployment files
.demoDeploy_*
.cursor/rules/nx-rules.mdc
.github/instructions/nx.instructions.md
