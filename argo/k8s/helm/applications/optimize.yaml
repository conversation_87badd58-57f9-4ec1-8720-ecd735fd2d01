apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: optimize-#{Octopus.Environment.Name}
  namespace: argocd
spec:
  project: portal-proj
  source:
    repoURL: "#{CC.Helm.Repo}"
    chart: "#{Service.PackageId}"
    targetRevision: "#{Octopus.Release.Number}"
    helm:
      parameters:
        - name: global.environment
          value: "#{Octopus.Environment.Name}"
        - name: global.version
          value: "#{Octopus.Release.Number}"
        - name: global.namespace
          value: "#{CC.Destination.Namespace}"
        - name: optimize-ui.enabled
          value: "#{Service.OptimizeUi.Enabled}"
        - name: keystone-webapp.enabled
          value: "#{Service.KeystoneWebapp.Enabled}"
        #-- Optimize --#
        - name: optimize-ui.labels.tags\.datadoghq\.com/env
          value: "#{Octopus.Environment.Name}"
        - name: optimize-ui.labels.tags\.datadoghq\.com/version
          value: "#{Octopus.Release.Number}"
        - name: optimize-ui.container.resources.requests.memory
          value: "#{Service.OptimizeUi.Container.Resources.Requests.Memory}"
        - name: optimize-ui.container.resources.requests.cpu
          value: "#{Service.OptimizeUi.Container.Resources.Requests.CPU}"
        - name: optimize-ui.container.resources.limits.memory
          value: "#{Service.OptimizeUi.Container.Resources.Limits.Memory}"
        - name: optimize-ui.container.resources.limits.cpu
          value: "#{Service.OptimizeUi.Container.Resources.Limits.CPU}"
        - name: optimize-ui.hpa.minReplicas
          value: "#{Service.OptimizeUi.HPA.MinReplicas}"
        - name: optimize-ui.hpa.maxReplicas
          value: "#{Service.OptimizeUi.HPA.MaxReplicas}"
        - name: optimize-ui.hpa.cpuUtilizationTarget
          value: "#{Service.OptimizeUi.HPA.CpuUtilizationTarget}"
        - name: optimize-ui.ingress.enabled
          value: "#{Service.OptimizeUi.Ingress.Enabled}"
        - name: optimize-ui.ingress.host
          value: "#{Service.OptimizeUi.Ingress.Host}"
        - name: optimize-ui.ingress.annotations.alb\.ingress\.kubernetes\.io/scheme
          value: "#{Service.OptimizeUi.Ingress.ALB.Scheme}"
        - name: optimize-ui.ingress.annotations.alb\.ingress\.kubernetes\.io/certificate-arn
          value: "#{Service.OptimizeUi.Ingress.ALB.Certificate.ARN}"
        - name: optimize-ui.ingress.annotations.alb\.ingress\.kubernetes\.io/group\.name
          value: "#{Service.OptimizeUi.Ingress.ALB.Group.Name}"
        - name: optimize-ui.env.ENV
          value: "#{Octopus.Environment.Name}"
        - name: optimize-ui.env.DATADOG_APPLICATION_ID
          value: "#{DATADOG_APPLICATION_ID}"
        - name: optimize-ui.env.DATADOG_CLIENT_TOKEN
          value: "#{DATADOG_CLIENT_TOKEN}"
        - name: optimize-ui.env.DATADOG_SAMPLE_RATE
          value: "#{DATADOG_SAMPLE_RATE}"
        - name: optimize-ui.env.DATADOG_APPLICATION_VERSION
          value: "#{DATADOG_APPLICATION_VERSION}"
        - name: optimize-ui.env.APIURI
          value: "#{APIURI}"
        - name: optimize-ui.env.AUTHAPIURI
          value: "#{AUTHAPIURI}"
        - name: optimize-ui.env.ELIGIBILITYIMPORTAPIURI
          value: "#{ELIGIBILITYIMPORTAPIURI}"
        - name: optimize-ui.env.EVALUATEAPIURI
          value: "#{EVALUATEAPIURI}"
        - name: optimize-ui.env.DATAMAPPERAPIURI
          value: "#{DATAMAPPERAPIURI}"
        - name: optimize-ui.env.SALESFORCEAPIURI
          value: "#{SALESFORCEAPIURI}"
        - name: optimize-ui.env.MEMBERISSUESAPIURI
          value: "#{MEMBERISSUESAPIURI}"
        - name: optimize-ui.env.MEMBERSEARCHAPIURI
          value: "#{MEMBERSEARCHAPIURI}"
        - name: optimize-ui.env.PLANPROVISIONAPIURI
          value: "#{PLANPROVISIONAPIURI}"
        - name: optimize-ui.env.TASKSERVICEAPIURI
          value: "#{TASKSERVICEAPIURI}"
        - name: optimize-ui.env.FMSINVOICEAPIURI
          value: "#{FMSINVOICEAPIURI}"
        - name: optimize-ui.env.NEXT_PUBLIC_BEN_ADMIN_URL
          value: '#{NEXT_PUBLIC_BEN_ADMIN_URL}'
        - name: optimize-ui.env.NEXT_PUBLIC_PROTECT_URL
          value: '#{NEXT_PUBLIC_PROTECT_URL}'
        - name: optimize-ui.env.SHOW_BEN_ADMIN_V2
          value: '#{SHOW_BEN_ADMIN_V2}'
        - name: optimize-ui.env.BILLINGAPIURI
          value: "#{BILLINGAPIURI}"
        - name: optimize-ui.env.COMMUNICATIONMANAGERAPIURI
          value: "#{COMMUNICATIONMANAGERAPIURI}" 
        - name: optimize-ui.env.MEMBERSHUBAPIURI
          value: "#{MEMBERSHUBAPIURI}"
        - name: optimize-ui.env.OKTA_PKCE
          value: "#{OKTA_PKCE}"
        - name: optimize-ui.env.OKTA_CLIENTID
          value: "#{OKTA_CLIENTID}"
        - name: optimize-ui.env.OKTA_REDIRECTURI
          value: "#{OPTIMIZE_OKTA_REDIRECTURI}"
        - name: optimize-ui.env.OKTA_ISSUER
          value: "#{OKTA_ISSUER}"
        - name: optimize-ui.env.OKTA_TOKENMANAGER_STORAGE
          value: "#{OKTA_TOKENMANAGER_STORAGE}"
        - name: optimize-ui.env.OKTA_TOKENMANAGER_AUTORENEW
          value: "#{OKTA_TOKENMANAGER_AUTORENEW}"
        - name: optimize-ui.env.OKTA_COOKIES_SECURE
          value: "#{OKTA_COOKIES_SECURE}"
        - name: optimize-ui.env.OKTA_MAXCLOCKSKEW
          value: "#{OKTA_MAXCLOCKSKEW}"
        - name: optimize-ui.env.DATANET_LOGIN_URL
          value: "#{DATANET_LOGIN_URL}"
        - name: optimize-ui.env.PORTAL_LOGIN_URL
          value: "#{PORTAL_LOGIN_URL}"
        - name: optimize-ui.env.FEEDBACK_FORM
          value: "#{FEEDBACK_FORM}"
        - name: optimize-ui.env.SESSION_TIMEOUT
          value: "#{SESSION_TIMEOUT}"
        - name: optimize-ui.env.STATIC_ASSETS_URL
          value: "#{STATIC_ASSETS_URL}"
        - name: optimize-ui.env.WRITE_SCHEMA_ID
          value: "#{WRITE_SCHEMA_ID}"
        - name: optimize-ui.env.QLIK_APP_ID
          value: "#{QLIK_APP_ID}"
        - name: optimize-ui.env.QLIK_SHEET_ID
          value: "#{QLIK_SHEET_ID}"        
        - name: optimize-ui.env.IMPORT_SCHEMA_WRITE_SCHEMA_ID
          value: "#{IMPORT_SCHEMA_WRITE_SCHEMA_ID}"        
        - name: optimize-ui.env.UNMAPPED_EDI_TEMPLATE_ID
          value: "#{UNMAPPED_EDI_TEMPLATE_ID}"
        - name: optimize-ui.env.AUDITAPIURI
          value: "#{AUDITAPIURI}"
        - name: optimize-ui.env.MEMBERSHUBAPIURI
          value: "#{MEMBERSHUBAPIURI}"
        - name: optimize-ui.env.ELIG_IMPORTS_QLIK_APPID
          value: "#{ELIG_IMPORTS_QLIK_APPID}"
        - name: optimize-ui.env.AUTH0_ISSUER_BASE_URL
          value: "#{AP_AUTH0_ISSUER_BASE_URL}"
        - name: optimize-ui.env.OPTIMIZE_AUTH0_CLIENT_ID
          value: "#{OPTIMIZE_AUTH0_CLIENT_ID}"
        - name: optimize-ui.env.AUTH0_REDIRECTURI
          value: "#{OPTIMIZE_AUTH0_REDIRECTURI}"
        #--------------#
        #-- TempCard Webapp --#
        - name: tempcard-webapp.labels.tags\.datadoghq\.com/env
          value: '#{Octopus.Environment.Name}'
        - name: tempcard-webapp.labels.tags\.datadoghq\.com/version
          value: '#{Octopus.Release.Number}'
        - name: tempcard-webapp.container.resources.requests.memory
          value: '#{Service.TempCardWebapp.Container.Resources.Requests.Memory}'
        - name: tempcard-webapp.container.resources.requests.cpu
          value: '#{Service.TempCardWebapp.Container.Resources.Requests.CPU}'
        - name: tempcard-webapp.container.resources.limits.memory
          value: '#{Service.TempCardWebapp.Container.Resources.Limits.Memory}'
        - name: tempcard-webapp.container.resources.limits.cpu
          value: '#{Service.TempCardWebapp.Container.Resources.Limits.CPU}'
        - name: tempcard-webapp.hpa.minReplicas
          value: '#{Service.TempCardWebapp.HPA.MinReplicas}'
        - name: tempcard-webapp.hpa.maxReplicas
          value: '#{Service.TempCardWebapp.HPA.MaxReplicas}'
        - name: tempcard-webapp.hpa.cpuUtilizationTarget
          value: '#{Service.TempCardWebapp.HPA.CpuUtilizationTarget}'
        - name: tempcard-webapp.ingress.enabled
          value: '#{Service.TempCardWebapp.Ingress.Enabled}'
        - name: tempcard-webapp.ingress.host
          value: '#{Service.TempCardWebapp.Ingress.Host}'
        - name: tempcard-webapp.ingress.annotations.alb\.ingress\.kubernetes\.io/scheme
          value: '#{Service.TempCardWebapp.Ingress.ALB.Scheme}'
        - name: tempcard-webapp.ingress.annotations.alb\.ingress\.kubernetes\.io/certificate-arn
          value: '#{Service.TempCardWebapp.Ingress.ALB.Certificate.ARN}'
        - name: tempcard-webapp.ingress.annotations.alb\.ingress\.kubernetes\.io/group\.name
          value: '#{Service.TempCardWebapp.Ingress.ALB.Group.Name}'
        - name: tempcard-webapp.env.ENV
          value: '#{Octopus.Environment.Name}'        
        - name: tempcard-webapp.env.TEMPCARDAPIURI
          value: '#{TEMPCARDAPIURI}'
        - name: tempcard-webapp.env.PORTALAPI
          value: '#{APIURI}'   #Same addr here as defined in optimize-ui
        #--------------#
        #-- Keystone Webapp --#
        - name: keystone-webapp.labels.tags\.datadoghq\.com/env
          value: "#{Octopus.Environment.Name}"
        - name: keystone-webapp.labels.tags\.datadoghq\.com/version
          value: "#{Octopus.Release.Number}"
        - name: keystone-webapp.container.resources.requests.memory
          value: "#{Service.KeystoneWebapp.Container.Resources.Requests.Memory}"
        - name: keystone-webapp.container.resources.requests.cpu
          value: "#{Service.KeystoneWebapp.Container.Resources.Requests.CPU}"
        - name: keystone-webapp.container.resources.limits.memory
          value: "#{Service.KeystoneWebapp.Container.Resources.Limits.Memory}"
        - name: keystone-webapp.container.resources.limits.cpu
          value: "#{Service.KeystoneWebapp.Container.Resources.Limits.CPU}"
        - name: keystone-webapp.hpa.minReplicas
          value: "#{Service.KeystoneWebapp.HPA.MinReplicas}"
        - name: keystone-webapp.hpa.maxReplicas
          value: "#{Service.KeystoneWebapp.HPA.MaxReplicas}"
        - name: keystone-webapp.hpa.cpuUtilizationTarget
          value: "#{Service.KeystoneWebapp.HPA.CpuUtilizationTarget}"
        - name: keystone-webapp.ingress.enabled
          value: "#{Service.KeystoneWebapp.Ingress.Enabled}"
        - name: keystone-webapp.ingress.host
          value: "#{Service.KeystoneWebapp.Ingress.Host}"
        - name: keystone-webapp.ingress.annotations.alb\.ingress\.kubernetes\.io/scheme
          value: "#{Service.KeystoneWebapp.Ingress.ALB.Scheme}"
        - name: keystone-webapp.ingress.annotations.alb\.ingress\.kubernetes\.io/certificate-arn
          value: "#{Service.KeystoneWebapp.Ingress.ALB.Certificate.ARN}"
        - name: keystone-webapp.ingress.annotations.alb\.ingress\.kubernetes\.io/group\.name
          value: "#{Service.KeystoneWebapp.Ingress.ALB.Group.Name}"
        - name: keystone-webapp.env.ENV
          value: "#{Octopus.Environment.Name}"
        - name: keystone-webapp.env.REFDATAAPIURI
          value: "#{REFDATAAPIURI}"
        - name: keystone-webapp.env.OKTA_PKCE
          value: "#{OKTA_PKCE}"
        - name: keystone-webapp.env.OKTA_CLIENTID
          value: "#{OKTA_CLIENTID}"
        - name: keystone-webapp.env.OKTA_REDIRECTURI
          value: "#{KEYSTONE_OKTA_REDIRECTURI}"
        - name: keystone-webapp.env.OKTA_ISSUER
          value: "#{OKTA_ISSUER}"
        - name: keystone-webapp.env.OKTA_TOKENMANAGER_STORAGE
          value: "#{OKTA_TOKENMANAGER_STORAGE}"
        - name: keystone-webapp.env.OKTA_TOKENMANAGER_AUTORENEW
          value: "#{OKTA_TOKENMANAGER_AUTORENEW}"
        - name: keystone-webapp.env.OKTA_COOKIES_SECURE
          value: "#{OKTA_COOKIES_SECURE}"
        - name: keystone-webapp.env.OKTA_MAXCLOCKSKEW
          value: "#{OKTA_MAXCLOCKSKEW}"
        - name: keystone-webapp.env.STATIC_ASSETS_URL
          value: "#{STATIC_ASSETS_URL}"
        - name: keystone-webapp.env.AUTH0_ISSUER_BASE_URL
          value: "#{AP_AUTH0_ISSUER_BASE_URL}"
        - name: keystone-webapp.env.AUTH0_CLIENT_ID
          value: "#{KEYSTONE_AUTH0_CLIENT_ID}"
        - name: keystone-webapp.env.OPTIMIZE_AUTH0_CLIENT_ID
          value: "#{OPTIMIZE_AUTH0_CLIENT_ID}"
        - name: keystone-webapp.env.AUTH0_REDIRECTURI
          value: "#{KEYSTONE_AUTH0_REDIRECTURI}"
        #---------------------#
        #-- Admin Portal --#
        - name: admin-portal.enabled
          value: "#{Service.AdminPortal.Enabled}"
        - name: admin-portal.labels.tags\.datadoghq\.com/env
          value: "#{Octopus.Environment.Name}"
        - name: admin-portal.labels.tags\.datadoghq\.com/version
          value: "#{Octopus.Release.Number}"
        - name: admin-portal.container.resources.requests.memory
          value: "#{Service.AdminPortal.Container.Resources.Requests.Memory}"
        - name: admin-portal.container.resources.requests.cpu
          value: "#{Service.AdminPortal.Container.Resources.Requests.CPU}"
        - name: admin-portal.container.resources.limits.memory
          value: "#{Service.AdminPortal.Container.Resources.Limits.Memory}"
        - name: admin-portal.container.resources.limits.cpu
          value: "#{Service.AdminPortal.Container.Resources.Limits.CPU}"
        - name: admin-portal.hpa.minReplicas
          value: "#{Service.AdminPortal.HPA.MinReplicas}"
        - name: admin-portal.hpa.maxReplicas
          value: "#{Service.AdminPortal.HPA.MaxReplicas}"
        - name: admin-portal.hpa.cpuUtilizationTarget
          value: "#{Service.AdminPortal.HPA.CpuUtilizationTarget}"
        - name: admin-portal.ingress.enabled
          value: "#{Service.AdminPortal.Ingress.Enabled}"
        - name: admin-portal.ingress.host
          value: "#{Service.AdminPortal.Ingress.Host}"
        - name: admin-portal.ingress.annotations.alb\.ingress\.kubernetes\.io/scheme
          value: "#{Service.AdminPortal.Ingress.ALB.Scheme}"
        - name: admin-portal.ingress.annotations.alb\.ingress\.kubernetes\.io/certificate-arn
          value: "#{Service.AdminPortal.Ingress.ALB.Certificate.ARN}"
        - name: admin-portal.ingress.annotations.alb\.ingress\.kubernetes\.io/group\.name
          value: "#{Service.AdminPortal.Ingress.ALB.Group.Name}"
        - name: admin-portal.env.ENV
          value: "#{Octopus.Environment.Name}"
        - name: admin-portal.env.AUDITAPIURI
          value: "#{AUDITAPIURI}"
        - name: admin-portal.env.TASKSERVICEAPIURI
          value: "#{TASKSERVICEAPIURI}"
        - name: admin-portal.env.PORT
          value: "#{Service.AdminPortal.PORT}"
        - name: admin-portal.env.STATIC_ASSETS_URL
          value: "#{STATIC_ASSETS_URL}"
        - name: admin-portal.env.AUTH0_BASE_URL
          value: '#{AP_AUTH0_BASE_URL}'
        - name: admin-portal.env.AUTH0_CLIENT_SECRET
          value: '#{AP_AUTH0_CLIENT_SECRET}'
        - name: admin-portal.env.AUTH0_SECRET
          value: '#{AP_AUTH0_SECRET}'
        - name: admin-portal.env.AUTH0_CLIENT_ID
          value: '#{AP_AUTH0_CLIENT_ID}'
        - name: admin-portal.env.AUTH0_ISSUER_BASE_URL
          value: '#{AP_AUTH0_ISSUER_BASE_URL}'
        - name: admin-portal.env.TOKEN
          value: '#{AP_CERT_TOKEN}'
        - name: admin-portal.env.AUTH0_NAMESPACE_KEY
          value: '#{AUTH0_NAMESPACE_KEY}'
        - name: admin-portal.env.ELIGIBILITYIMPORTAPIURI
          value: '#{ELIGIBILITYIMPORTAPIURI}'
        - name: admin-portal.env.FMSINVOICEAPIURI
          value: '#{FMSINVOICEAPIURI}'
        - name: admin-portal.env.NEXT_PUBLIC_BEN_ADMIN_URL
          value: '#{NEXT_PUBLIC_BEN_ADMIN_URL}'
        - name: admin-portal.env.NEXT_PUBLIC_PROTECT_URL
          value: '#{NEXT_PUBLIC_PROTECT_URL}'
        - name: admin-portal.env.SHOW_BEN_ADMIN_V2
          value: '#{SHOW_BEN_ADMIN_V2}'
        - name: admin-portal.env.FMSCOMMISSIONAPIURI
          value: '#{FMSCOMMISSIONAPIURI}'
        - name: admin-portal.env.BMSAPIURI
          value: '#{BMSAPIURI}'
        - name: admin-portal.env.FMSBILLINGSERVICEURI
          value: '#{FMSBILLINGSERVICEURI}'
        - name: admin-portal.env.AP_DATADOG_APPLICATION_ID
          value: '#{AP_DATADOG_APPLICATION_ID}'
        - name: admin-portal.env.AP_DATADOG_CLIENT_TOKEN
          value: '#{DATADOG_CLIENT_TOKEN}'
        - name: admin-portal.env.DATADOG_SAMPLE_RATE
          value: '#{DATADOG_SAMPLE_RATE}'
        - name: admin-portal.env.DATADOG_APPLICATION_VERSION
          value: '#{DATADOG_APPLICATION_VERSION}'
        - name: admin-portal.env.ENV
          value: '#{Octopus.Environment.Name}'
        - name: admin-portal.env.RXPA_ENABLED
          value: "#{RXPA_ENABLED}"
        - name: admin-portal.env.ELIG_IMPORTS_QLIK_APPID
          value: "#{ELIG_IMPORTS_QLIK_APPID}"
        - name: admin-portal.env.NODE_OPTIONS
          value: "#{AP_NODE_OPTIONS}"
        #---------------#
        #-- Member Portal --#
        - name: member-portal.enabled
          value: "#{Service.MemberPortal.Enabled}"
        - name: member-portal.labels.tags\.datadoghq\.com/env
          value: "#{Octopus.Environment.Name}"
        - name: member-portal.labels.tags\.datadoghq\.com/version
          value: "#{Octopus.Release.Number}"
        - name: member-portal.container.resources.requests.memory
          value: "#{Service.MemberPortal.Container.Resources.Requests.Memory}"
        - name: member-portal.container.resources.requests.cpu
          value: "#{Service.MemberPortal.Container.Resources.Requests.CPU}"
        - name: member-portal.container.resources.limits.memory
          value: "#{Service.MemberPortal.Container.Resources.Limits.Memory}"
        - name: member-portal.container.resources.limits.cpu
          value: "#{Service.MemberPortal.Container.Resources.Limits.CPU}"
        - name: member-portal.hpa.minReplicas
          value: "#{Service.MemberPortal.HPA.MinReplicas}"
        - name: member-portal.hpa.maxReplicas
          value: "#{Service.MemberPortal.HPA.MaxReplicas}"
        - name: member-portal.hpa.cpuUtilizationTarget
          value: "#{Service.MemberPortal.HPA.CpuUtilizationTarget}"
        - name: member-portal.ingress.enabled
          value: "#{Service.MemberPortal.Ingress.Enabled}"
        - name: member-portal.ingress.host
          value: "#{Service.MemberPortal.Ingress.Host}"
        - name: member-portal.ingress.annotations.alb\.ingress\.kubernetes\.io/scheme
          value: "#{Service.MemberPortal.Ingress.ALB.Scheme}"
        - name: member-portal.ingress.annotations.alb\.ingress\.kubernetes\.io/certificate-arn
          value: "#{Service.MemberPortal.Ingress.ALB.Certificate.ARN}"
        - name: member-portal.ingress.annotations.alb\.ingress\.kubernetes\.io/group\.name
          value: "#{Service.MemberPortal.Ingress.ALB.Group.Name}"
        - name: member-portal.env.ENV
          value: "#{Octopus.Environment.Name}"
        - name: member-portal.env.PORT
          value: "#{Service.MemberPortal.PORT}"
        - name: member-portal.env.STATIC_ASSETS_URL
          value: "#{STATIC_ASSETS_URL}"
        - name: member-portal.env.AUTH0_BASE_URL
          value: '#{MP_AUTH0_BASE_URL}'
        - name: member-portal.env.AUTH0_CLIENT_SECRET
          value: '#{MP_AUTH0_CLIENT_SECRET}'
        - name: member-portal.env.AUTH0_SECRET
          value: '#{MP_AUTH0_SECRET}'
        - name: member-portal.env.AUTH0_CLIENT_ID
          value: '#{MP_AUTH0_CLIENT_ID}'
        - name: member-portal.env.AUTH0_ISSUER_BASE_URL
          value: '#{MP_AUTH0_ISSUER_BASE_URL}'
        - name: member-portal.env.AUTH0_NAMESPACE_KEY
          value: '#{AUTH0_NAMESPACE_KEY}'
        - name: member-portal.env.MP_DATADOG_APPLICATION_ID
          value: '#{MP_DATADOG_APPLICATION_ID}'
        - name: member-portal.env.MP_DATADOG_CLIENT_TOKEN
          value: '#{DATADOG_CLIENT_TOKEN}'
        - name: member-portal.env.DATADOG_SAMPLE_RATE
          value: '#{DATADOG_SAMPLE_RATE}'
        - name: member-portal.env.DATADOG_APPLICATION_VERSION
          value: '#{DATADOG_APPLICATION_VERSION}'
        - name: member-portal.env.ENV
          value: '#{Octopus.Environment.Name}'
        #---------------#
      releaseName: omp
  destination:
    server: "#{CC.Destination.Server}"
    namespace: "#{CC.Destination.Namespace}"
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
