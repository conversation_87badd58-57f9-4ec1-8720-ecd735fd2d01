'use client';
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>tonG<PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>er,
  <PERSON>er<PERSON><PERSON>,
  Drawer<PERSON>lose<PERSON>utton,
  <PERSON>er<PERSON><PERSON>nt,
  DrawerHeader,
  DrawerOverlay,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Heading,
  HStack,
  Input,
  Select,
  Text,
  Tooltip,
} from '@chakra-ui/react';
import { CustomReactSelect, ReactSelectListItem } from '@next/admin/components';
import { FILE_UPLOAD_TOOLTIP_MESSAGES, theme } from '@next/admin/constants';
import { useApi } from '@next/shared/api';
import { useCustomToast } from '@next/shared/hooks';
import React, { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { AiOutlineQuestionCircle } from 'react-icons/ai';
import ReactSelect from 'react-select';

type UploadFileProps = {
  isOpen: boolean;
  onClose: () => void;
  onFileUpload: () => void;
};

type FileData = {
  file: FileList | null;
  categoryId: number;
  phi: boolean;
  organizationNo: number | string;
  recipientId: { label: string; value: string }[];
};

export const UploadFile: React.FC<UploadFileProps> = ({
  isOpen,
  onClose,
  onFileUpload,
}) => {
  const [orgList, setOrgList] = useState<ReactSelectListItem[]>([
    {
      label: '',
      value: '',
    },
  ]);

  const [selectedOrganization, setSelectedOrganization] = useState<
    number | null | string
  >(null);

  const {
    handleSubmit,
    register,
    control,
    formState: { isSubmitting, errors },
    reset,
    trigger,
  } = useForm<FileData>();

  const showToast = useCustomToast();

  const { methodApi, getApi, organizations, orgSpecificUsers } = useApi();

  const filterData = (field: Record<string, any>, value: string | null) => {
    field.onChange(value || '');
    setSelectedOrganization(value || '');
    getApi('orgSpecificUsers', {
      orgId: value || '',
    });
  };
  const handleValidation = (value: FileList): string | boolean => {
    const acceptedFormats = [
      'csv',
      'doc',
      'docx',
      'pdf',
      'ppt',
      'pptx',
      'xls',
      'xlsx',
      'zip',
      'txt',
    ];
    const fileExtension = value[0]?.name?.split('.')?.pop()?.toLowerCase();
    if (!acceptedFormats.includes(fileExtension as string)) {
      return 'Supported file types: .csv, .doc, .docx, .txt, .pdf, .ppt, .pptx, .xls, .xlsx, .zip';
    }
    return true;
  };
  const onSubmit = async (values: FileData) => {
    if (values?.file) {
      const formData = new FormData();
      formData.append('file', values.file[0]);
      formData.append('categoryId', String(values.categoryId));
      formData.append('phi', String(values.phi));
      formData.append('organizationNo', String(values.organizationNo));
      formData.append(
        'recipientId',
        JSON.stringify(
          values.recipientId.map((recipient) => {
            return String(recipient?.value);
          })
        )
      );

      methodApi('fileUpload', {
        method: 'POST',
        body: formData,
        sendType: 'multipart/form-data',
        onSuccess: () => {
          reset();
          onClose();
          onFileUpload();
        },
        onError: (response: Record<string, any>) => {
          const errDetail = response?.detail.split('Error:');
          showToast({
            title: 'Upload File',
            description: errDetail[1],
            status: 'error',
          });
        },
      });
    }
  };

  const usersList =
    orgSpecificUsers?.length > 0
      ? orgSpecificUsers?.map((user: Record<string, any>) => ({
          label: user.firstName + ' ' + user.lastName,
          value: user.userId || '',
        }))
      : [];

  useEffect(() => {
    if (organizations) {
      const orgDataList = organizations?.data?.map(
        (item: Record<string, any>) => ({
          label: item?.name,
          value: item?.organizationNo?.toString(),
        })
      );
      setOrgList(orgDataList);
    }
  }, [organizations]);

  return (
    <Drawer isOpen={isOpen} placement="right" onClose={onClose} size={'lg'}>
      <DrawerOverlay />
      <DrawerContent>
        <DrawerHeader
          borderBottom={`1px solid ${theme.colors.brand.lightgray}`}
        >
          <DrawerCloseButton />
          Upload File
        </DrawerHeader>

        <DrawerBody background={theme.colors.brand.lightestgray}>
          <Flex
            backgroundColor={theme.colors.brand.white}
            borderRadius={6}
            boxShadow="rgba(0, 0, 0, 0.12) 0px 4px 4px"
            padding={4}
          >
            <form
              onSubmit={handleSubmit((values) => onSubmit(values))}
              style={{ width: '100%' }}
            >
              <Flex alignItems="center" gap={8} flexDirection={'column'}>
                <FormControl flex={6} isInvalid={!!errors.file}>
                  <HStack marginBottom={2} spacing={1} alignItems={'center'}>
                    <FormLabel margin={0} htmlFor="name">
                      <Text as="span" marginRight={2} color="red">
                        *
                      </Text>
                      File Upload
                    </FormLabel>
                    <Tooltip
                      label={FILE_UPLOAD_TOOLTIP_MESSAGES.upload}
                      hasArrow
                      placement="top"
                      bg={theme.colors.brand.black}
                      borderRadius={5}
                    >
                      <span>
                        <AiOutlineQuestionCircle size={18} />
                      </span>
                    </Tooltip>
                  </HStack>
                  <Input
                    id="file"
                    type="file"
                    {...register('file', {
                      required: { value: true, message: 'File is required' },
                      validate: (value: any) => handleValidation(value),
                      onChange: () => trigger('file'),
                    })}
                    _hover={{
                      border: `2px solid #3182ce`,
                    }}
                  />
                  <FormErrorMessage>
                    {errors && errors?.file?.message}
                  </FormErrorMessage>
                </FormControl>

                <FormControl flex={2} isInvalid={!!errors.categoryId}>
                  <HStack marginBottom={2} spacing={1} alignItems={'center'}>
                    <FormLabel margin={0} htmlFor="category">
                      <Text as="span" marginRight={2} color="red">
                        *
                      </Text>
                      File Category
                    </FormLabel>
                    <Tooltip
                      label={FILE_UPLOAD_TOOLTIP_MESSAGES.category}
                      hasArrow
                      placement="top"
                      bg={theme.colors.brand.black}
                      borderRadius={5}
                    >
                      <span>
                        <AiOutlineQuestionCircle size={18} />
                      </span>
                    </Tooltip>
                  </HStack>

                  <Select
                    placeholder="Select a category"
                    id="category"
                    {...register('categoryId', { required: true })}
                    _hover={{
                      border: `2px solid #3182ce`,
                    }}
                  >
                    <option value="1">Claims</option>
                    <option value="2">Eligibility</option>
                    <option value="3">Financials</option>
                    <option value="4">Other</option>
                  </Select>
                  <FormErrorMessage>
                    {errors.categoryId && 'Category is required'}
                  </FormErrorMessage>
                </FormControl>

                <FormControl flex={6}>
                  <HStack marginBottom={2} spacing={1} alignItems={'center'}>
                    <Checkbox id="phi" defaultChecked {...register('phi')}>
                      File contains Protected Health Information (PHI)
                      <Text
                        display={'inline'}
                        marginLeft={2}
                        padding={2}
                        fontSize={10}
                        border={`1px solid ${theme.colors.brand.yellow200}`}
                        backgroundColor={theme.colors.brand.yellow100}
                        textColor={theme.colors.brand.brown100}
                      >
                        PHI
                      </Text>
                    </Checkbox>
                  </HStack>
                </FormControl>
              </Flex>
              <Divider marginY={4} />

              <Heading fontSize={18} marginBottom={6}>
                File Access
              </Heading>

              <Box marginBottom={4}>
                <FormLabel htmlFor="organization" marginBottom={2}>
                  <Text as="span" marginRight={2} color="red">
                    *
                  </Text>
                  Organization
                </FormLabel>

                {orgList?.length > 0 && (
                  <FormControl flex={2} isInvalid={!!errors.organizationNo}>
                    <Controller
                      name="organizationNo"
                      control={control}
                      rules={{ required: true }}
                      render={({ field }) => (
                        <CustomReactSelect
                          data={orgList}
                          filterData={(values) => filterData(field, values)}
                        />
                      )}
                    />

                    <FormErrorMessage>
                      {errors.organizationNo && 'Organization is required'}
                    </FormErrorMessage>
                  </FormControl>
                )}
              </Box>

              {selectedOrganization && (
                <Box marginBottom={4}>
                  <HStack marginBottom={2} spacing={1} alignItems={'center'}>
                    <FormLabel margin={0} htmlFor="recipientId">
                      <Text as="span" marginRight={2} color="red">
                        *
                      </Text>
                      Recipients
                    </FormLabel>
                    <Tooltip
                      label={FILE_UPLOAD_TOOLTIP_MESSAGES.recipients}
                      hasArrow
                      placement="top"
                      bg={theme.colors.brand.black}
                      borderRadius={5}
                    >
                      <span>
                        <AiOutlineQuestionCircle size={18} />
                      </span>
                    </Tooltip>
                  </HStack>

                  {orgSpecificUsers && (
                    <FormControl flex={2} isInvalid={!!errors.recipientId}>
                      <Controller
                        name="recipientId"
                        control={control}
                        rules={{ required: true }}
                        render={({ field }) => (
                          <ReactSelect
                            {...field}
                            isMulti
                            options={usersList}
                            isClearable={true}
                          />
                        )}
                      />

                      <FormErrorMessage>
                        {errors.recipientId && 'Atleast one User is required'}
                      </FormErrorMessage>
                    </FormControl>
                  )}
                </Box>
              )}

              <Divider marginY={4} />

              <Flex alignItems="center" justifyContent="flex-end" mt={4}>
                <ButtonGroup spacing="4">
                  <Button
                    onClick={() => {
                      reset();
                      onClose();
                    }}
                    variant="outline"
                    type="submit"
                    colorScheme="brand"
                    borderColor="brand.700"
                    color="brand.700"
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="solid"
                    isLoading={isSubmitting}
                    type="submit"
                    colorScheme="brand"
                    bg="brand.700"
                  >
                    Upload
                  </Button>
                </ButtonGroup>
              </Flex>
            </form>
          </Flex>
        </DrawerBody>
      </DrawerContent>
    </Drawer>
  );
};
