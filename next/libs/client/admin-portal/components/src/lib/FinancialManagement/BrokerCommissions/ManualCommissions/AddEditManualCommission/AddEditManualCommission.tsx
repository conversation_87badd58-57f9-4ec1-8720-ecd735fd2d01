import {
  AlertDialog,
  AlertDialogBody,
  AlertDialogCloseButton,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogOverlay,
  Box,
  Button,
  Flex,
  ModalBody,
  ModalCloseButton,
  ModalHeader,
  Spacer,
  useDisclosure,
} from '@chakra-ui/react';
import { createStandaloneToast } from '@chakra-ui/react';
import { useJavaApi } from '@next/shared/api';
import { useRouteChangeValidatorOnFormUpdate } from '@next/shared/hooks';
import { ManualFieldType } from '@next/shared/types';
import { FormFields, RightSideModal } from '@next/shared/ui';
import moment from 'moment';
import { useEffect, useMemo, useRef, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

import { getFormItems } from './AddEditManualCommissionFormItems';

const { toast } = createStandaloneToast();

type IAddEditManualCommissionProps = {
  onRefreshList: () => void;
  onOpen: () => void;
  onClose: () => void;
  isOpen: boolean;
  selectedManualCommission: ManualFieldType;
  setSelectedManualCommission: (manualField: ManualFieldType) => void;
};

const CONFIRMATION_ACTIONS = {
  EDIT: 'EDIT',
  DELETE: 'DELETE',
};

export const AddEditManualCommission = ({
  selectedManualCommission,
  setSelectedManualCommission,
  onRefreshList,
  onOpen,
  onClose,
  isOpen,
}: IAddEditManualCommissionProps) => {
  const cancelRef = useRef(null);
  const formInstance = useForm();
  const {
    isOpen: warningIsOpen,
    onOpen: onWarningOpen,
    onClose: onWarningClose,
  } = useDisclosure();
  const { getApi, methodApi } = useJavaApi();
  const [brokerEntityOptions, setBrokerEntityOptions] = useState<Array<any>>(
    []
  );

  const [datesOptions, setDatesOptions] = useState<Array<any>>([]);

  const { brokerCommissionManualNo: manualCommissionId } =
    selectedManualCommission || {};
  const isEdit = !!manualCommissionId;
  const [glIdOptions, setGlIdOptions] = useState<Array<any>>([]);
  const [glRecords, setGlRecords] = useState<Array<any>>([]);
  const [initialFormValue, setInitialFormValue] = useState<object>({});
  const [isLoading, setIsLoading] = useState(false);
  const [confirmDialogTitle, setConfirmDialogTitle] = useState<string>('');
  const [confirmDialogMessage, setConfirmDialogMessage] = useState<string>('');
  const [confirmationAction, setConfirmationAction] = useState<string>(
    CONFIRMATION_ACTIONS.EDIT
  );

  const formItems = useMemo(
    () =>
      getFormItems({
        brokerEntityOptions,
        glIdOptions,
        isLoading,
        glRecords,
        datesOptions,
      }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [brokerEntityOptions, glIdOptions, isLoading, datesOptions]
  );
  const { isFormChanged } = useRouteChangeValidatorOnFormUpdate({
    initialFormValue,
    formInstance,
    isActive: isOpen,
  });

  useEffect(() => {
    getFormDropdownData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (!isOpen) {
      return;
    }
    if (!isEdit) {
      formInstance.reset({});
      setInitialFormValue({});
    } else {
      const {
        commBrokerNo,
        commTypeNo,
        paymentDueDate: paymentDueDateStr,
        commPeriodNo,
        commissionAmount,
        reason,
      } = selectedManualCommission;
      const paymentDueDate = moment(paymentDueDateStr, 'YYYY-MM-DD').toDate();
      formInstance.reset({
        commBrokerNo,
        commTypeNo,
        paymentDueDate,
        commPeriodNo,
        commissionAmount,
        reason,
      });
      setInitialFormValue({
        commBrokerNo,
        commTypeNo,
        paymentDueDate,
        commPeriodNo,
        commissionAmount,
        reason,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedManualCommission, isOpen]);

  const getFormDropdownData = async () => {
    await getGlIdOptions();
    await getBrokerEntityOptions();
    await getBillingPeriodOptions();
  };

  const getGlIdOptions = async () => {
    const response = await getApi('commissionGlAccounts');
    const glIds = response?.map(({ commTypeNo, name }: any) => ({
      value: commTypeNo,
      label: name,
    }));
    setGlRecords(response);
    setGlIdOptions(glIds);
  };

  const getBillingPeriodOptions = async () => {
    const response = await getApi('comPeriodList');

    const dates = response?.map(({ commPeriodNo, yearMonth }: any) => {
      const [year, month] = yearMonth.split('-');
      const date = new Date(Date.UTC(Number(year), Number(month) - 1, 2));
      const monthName = date.toLocaleString('default', {
        month: 'long',
        timeZone: 'UTC',
      });

      const lastTwoDigitsOfYear = year.slice(-2);
      const formattedDate = `${monthName} ${lastTwoDigitsOfYear}`;
      return {
        value: commPeriodNo,
        label: formattedDate,
      };
    });
    setDatesOptions(dates);
  };

  const getFlatEntityList = (inputArray: Array<any>) => {
    const finalArray: Array<any> = [];
    inputArray.forEach((item) => {
      finalArray.push(item);
      getChildren(item);
    });

    function getChildren(parentItem: any) {
      parentItem.children.forEach((item: any) => {
        finalArray.push(item);
        getChildren(item);
      });
    }
    return finalArray;
  };

  const getBrokerEntityOptions = async () => {
    const response = await getApi('treeList');
    const flatResponse = getFlatEntityList(response);
    const options = flatResponse?.map(({ commBrokerNo, name }: any) => ({
      value: commBrokerNo,
      label: name,
    }));
    setBrokerEntityOptions(options);
  };

  const closeModal = (ignoreFormCheck: any = false) => {
    if (ignoreFormCheck === true || !isFormChanged()) {
      onClose();
      setSelectedManualCommission({} as any);
      formInstance.reset({});
    }
  };
  // @ts-ignore
  const closeAndRefreshList = (ignoreFormCheck = false) => {
    closeModal(ignoreFormCheck);
    onRefreshList();
  };

  const callCreateManualCommission = async (apiData: any) => {
    await methodApi('createManualCommission', {
      data: apiData,
      notificationTitle: 'Create Manual Commission',
      method: 'POST',
    });
    toast({
      title: 'Create Manual Commission',
      description: 'success!',
      status: 'success',
      duration: 3000,
    });
  };

  const callUpdateManualCommission = async (apiData: any) => {
    const { versionNumber = 1 } = selectedManualCommission;
    await methodApi('updateDeleteManualCommission', {
      data: {
        ...apiData,
        brokerCommissionManualNo: manualCommissionId,
        versionNumber,
      },
      restParams: { manualCommissionId },
      notificationTitle: 'Update Manual Commission',
      method: 'PUT',
    });
    toast({
      title: 'Update Manual Commission',
      description: 'success!',
      status: 'success',
      duration: 3000,
    });
  };

  const onSubmitWithConfirmation = () => {
    if (!isEdit) {
      onSubmitForm();
      return;
    }
    setConfirmationAction(CONFIRMATION_ACTIONS.EDIT);
    setConfirmDialogTitle('Confirm Update');
    setConfirmDialogMessage('Are you sure you want to update this record?');
    onWarningOpen();
  };

  const openDeleteDialog = () => {
    setConfirmationAction(CONFIRMATION_ACTIONS.DELETE);
    setConfirmDialogTitle('Confirm Delete');
    setConfirmDialogMessage('Are you sure you want to delete this record?');
    onWarningOpen();
  };

  const handleConfirmAction = () => {
    onWarningClose();
    if (confirmationAction === CONFIRMATION_ACTIONS.EDIT) {
      onSubmitForm();
    } else {
      onDelete();
    }
  };

  const onSubmitForm = async () => {
    if (isLoading) {
      return;
    }
    const formData = formInstance.getValues();
    const paymentDueDate = moment(formData.paymentDueDate).format('YYYY-MM-DD');
    const apiData = { ...formData, paymentDueDate };
    setIsLoading(true);
    try {
      if (!isEdit) {
        await callCreateManualCommission(apiData);
      } else {
        await callUpdateManualCommission(apiData);
      }
      closeAndRefreshList(true);
    } catch (e) {
      // Do nothing
    } finally {
      setIsLoading(false);
    }
  };

  const onDelete = async () => {
    setIsLoading(true);
    try {
      await methodApi('updateDeleteManualCommission', {
        method: 'DELETE',
        notificationTitle: 'Delete Manual Commission',
        restParams: { manualCommissionId },
      });
      toast({
        title: 'Delete Manual Commission',
        description: 'success!',
        status: 'success',
        duration: 3000,
      });
      closeAndRefreshList();
    } catch (e) {
      // Do nothing
    } finally {
      setIsLoading(false);
    }
  };

  const openFormForAddManualCommission = () => {
    onOpen();
  };

  const renderAddButton = () => {
    return (
      <Button
        colorScheme="blue"
        variant="solid"
        size={'sm'}
        onClick={openFormForAddManualCommission}
        m={0}
      >
        Add Manual Payments
      </Button>
    );
  };

  const renderForm = () => {
    return (
      <>
        {/*  @ts-ignore - Ignored this due to the error from the react hook form */}
        <FormProvider {...formInstance}>
          <RightSideModal isOpen={isOpen} onClose={closeModal}>
            <ModalCloseButton left={'20px'} top={'16px'} />
            <ModalHeader pl={'60px'}>
              {isEdit ? 'Edit' : 'Add'} Manual Payment
            </ModalHeader>
            <ModalBody pt={0} overflow={'auto'}>
              <Box>
                <form
                  onSubmit={formInstance.handleSubmit(onSubmitWithConfirmation)}
                >
                  <FormFields formItems={formItems} />
                  <Flex minWidth="max-content" mt={'20px'}>
                    {isEdit && (
                      <Button
                        colorScheme="blue"
                        variant="solid"
                        onClick={openDeleteDialog}
                        size="sm"
                        isDisabled={isLoading}
                      >
                        Delete
                      </Button>
                    )}
                    <Spacer />
                    <Flex alignItems="center" gap="2">
                      <Button
                        variant="solid"
                        onClick={closeModal}
                        size="sm"
                        isDisabled={isLoading}
                      >
                        Cancel
                      </Button>
                      <Button
                        colorScheme="blue"
                        variant="solid"
                        type={'submit'}
                        size="sm"
                        isDisabled={isLoading}
                      >
                        Apply
                      </Button>
                    </Flex>
                  </Flex>
                </form>
              </Box>
            </ModalBody>
          </RightSideModal>
        </FormProvider>
      </>
    );
  };

  const renderAlertDialog = () => {
    return (
      <AlertDialog
        leastDestructiveRef={cancelRef}
        onClose={onWarningClose}
        isOpen={warningIsOpen}
        isCentered
        closeOnOverlayClick={false}
        closeOnEsc={false}
      >
        <AlertDialogOverlay />

        <AlertDialogContent>
          <AlertDialogHeader>{confirmDialogTitle}</AlertDialogHeader>
          <AlertDialogCloseButton />
          <AlertDialogBody>{confirmDialogMessage}</AlertDialogBody>
          <AlertDialogFooter>
            <Button ref={cancelRef} onClick={onWarningClose}>
              No
            </Button>
            <Button colorScheme="red" ml={3} onClick={handleConfirmAction}>
              Yes
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    );
  };

  return (
    <>
      {renderAddButton()}
      {renderForm()}
      {renderAlertDialog()}
    </>
  );
};
