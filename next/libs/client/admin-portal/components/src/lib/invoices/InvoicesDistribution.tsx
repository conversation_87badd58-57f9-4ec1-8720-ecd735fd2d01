import { Box, Checkbox, Flex, Radio, Stack, Text } from '@chakra-ui/react';
import {
  DISTRIBUTION_ARRAY,
  invoicesProps,
  invoiceTheme,
} from '@next/admin/constants';
import { useJavaApi } from '@next/shared/api';
import { useCustomToast } from '@next/shared/hooks';
import { useMemo } from 'react';
import { useFormContext } from 'react-hook-form';

import { AlertBox } from '../Global/AlertBox';
import { EmailInTables } from './EmailInTables';
import { InvoicesInput } from './InvoicesInput';
import { InvoicesSelect } from './InvoicesSelect';

interface FtpSite {
  value: string;
  label: string;
}

interface eligibilityConfigEncryptionkey {
  encryptionKeyNo: number;
  name: string;
}

type InvoicesDistributionProps = {
  targetList: FtpSite[];
  eligibilityConfigEncryptionkey: eligibilityConfigEncryptionkey[];
};
export const InvoicesDistribution = ({
  targetList,
  eligibilityConfigEncryptionkey,
}: InvoicesDistributionProps) => {
  type RadioBoxProps = {
    label: string;
    value: string;
  };
  type TargetFTPViewProps = {
    label: string;
    value: string;
  };

  const { register, watch, setValue } = useFormContext<invoicesProps>();

  const showToast = useCustomToast();

  const { getApi, targetFtpDetails = [] } = useJavaApi();

  const encryptionList = useMemo(
    () =>
      eligibilityConfigEncryptionkey?.map((item: any) => ({
        label: item.name,
        value: item.encryptionKeyNo,
      })) || [],
    [eligibilityConfigEncryptionkey]
  );

  useMemo(
    () =>
      watch('ftpSiteNo') &&
      watch('ftpSiteNo') !== '' &&
      getApi('targetFtpDetails', {
        ftpSiteNo: watch('ftpSiteNo') as string,
      }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [targetList, watch('ftpSiteNo')]
  );

  const addHandler = (email: string | undefined) => {
    const emailPattern = /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]*$/i;
    if (!email) return;
    email = email.toLowerCase().trim();
    if (!emailPattern.test(email)) {
      showToast({
        title: 'Enter Correct Email',
        status: 'error',
      });
      return;
    }
    const value = watch('distributionList')?.split(';') || [];
    value.push(email);
    setValue('distributionList', value.join(';'), { shouldDirty: true });
  };

  const getEmailsValue = () =>
    watch('distributionList')
      ?.split(';')
      ?.map((item) => ({ email: item })) || [];

  const removeHandler = (email: string) => {
    const value = watch('distributionList')
      ?.split(';')
      .filter((item) => item !== email);
    setValue(
      'distributionList',
      value?.length === 0 ? undefined : value?.join(';'),
      { shouldDirty: true }
    );
  };

  const onChangeHandler = (e: any) => {
    setValue('distribution', e.target.value, { shouldDirty: true });
    if (e.target.value === '0') {
      setValue('isEncrypter', false);
      setValue('ftpSiteNo', '');
      setValue('targetDirectory', '');
      setValue('distributionList', undefined);
      setValue('encryptionKeyNo', '');
    } else if (e.target.value === '1') {
      setValue('distributionList', undefined);
    } else if (e.target.value === '2') {
      setValue('ftpSiteNo', '');
      setValue('targetDirectory', '');
    } else if (e.target.value === 3) {
      setValue('distributionList', undefined);
      setValue('ftpSiteNo', '');
      setValue('targetDirectory', '');
    }
  };

  const RadioBox = ({ label, value }: RadioBoxProps) => (
    <Radio
      value={value}
      {...register('distribution')}
      defaultChecked={watch('distribution') === value}
      onChange={onChangeHandler}
    >
      <Text
        style={{
          fontSize: 12,
          color: '#06096a',
          marginBottom: 0,
          whiteSpace: 'nowrap',
        }}
      >
        {label}
      </Text>
    </Radio>
  );

  const TargetFTPView = ({ label, value }: TargetFTPViewProps) => {
    return (
      <Flex alignItems={'center'} gap={3}>
        <Text style={invoiceTheme.invoicesLabel}>{label}:</Text>
        <Text fontSize={12}>{value}</Text>
      </Flex>
    );
  };

  const checkTargetDirectory = () => {
    if (!watch('ftpSiteNo') || watch('ftpSiteNo') === '') {
      showToast({
        title: 'Select FTP target site',
        status: 'error',
      });
      return;
    }

    getApi(
      'targetDirectory',
      {
        ftpSiteNo: watch('ftpSiteNo') as string,
        directory: watch('targetDirectory'),
      },
      {
        onSuccess: (res) => {
          showToast({
            title: res.object,
            status: 'success',
          });
        },
        onError: (err) => {
          showToast({
            title: err.data[0],
            status: 'error',
          });
        },
      }
    );
  };

  return (
    <Flex gap={3}>
      <Text style={invoiceTheme.invoicesLabel}>Distribution:</Text>
      <Flex>
        <Stack direction="column">
          {DISTRIBUTION_ARRAY.map((item: RadioBoxProps, index) => (
            <RadioBox key={index} label={item.label} value={item.value} />
          ))}
        </Stack>
        <Box>
          <Flex flexDirection={'row'}>
            {(watch('distribution') === '1' ||
              watch('distribution') === '3') && (
              <Box>
                <InvoicesSelect
                  label="Target FTP Site*"
                  data={targetList}
                  name="ftpSiteNo"
                />
                {targetFtpDetails.length > 0 && watch('ftpSiteNo') !== '' && (
                  <Box ml={110} mt={3}>
                    <TargetFTPView
                      label="Host/URL/IP"
                      value={targetFtpDetails[0].host}
                    />
                    <TargetFTPView
                      label="Connection Type"
                      value={targetFtpDetails[0].displayValue}
                    />
                    <TargetFTPView
                      label="Port"
                      value={targetFtpDetails[0].port}
                    />
                    <TargetFTPView
                      label="User Name"
                      value={targetFtpDetails[0].userName}
                    />
                  </Box>
                )}
                <InvoicesInput
                  label="Target Directory"
                  name="targetDirectory"
                />
                <InvoicesInput label="File Name" name="ftpFileName" />
                <InvoicesInput label="File Pattern" name="filePattern" />
                <Box mt={3} ml={167}>
                  <AlertBox
                    alertButtonText={'Test Connection'}
                    approveButtonHandler={checkTargetDirectory}
                    approveButtonText="Ok"
                    rejectButtonText="Cancel"
                    bodyText={['Test FTP Connection?']}
                    style={invoiceTheme.invoicesButton}
                    headerText="Confirmation"
                  />
                </Box>
              </Box>
            )}
            {['2', '3'].includes(watch('distribution')) && (
              <Box ml={3} width={450}>
                <EmailInTables
                  data={getEmailsValue()}
                  addHandler={addHandler}
                  removeHandler={removeHandler}
                />
              </Box>
            )}
          </Flex>

          {watch('distribution') && watch('distribution') !== '0' && (
            <Box>
              <Checkbox
                {...register('isEncrypter')}
                style={{
                  marginTop: 10,
                  marginLeft: 165,
                }}
                value={'true'}
              >
                <Text
                  style={{
                    fontSize: 12,
                    color: '#06096a',
                    marginBottom: 0,
                    whiteSpace: 'nowrap',
                  }}
                >
                  Encrypt Generated Invoices?
                </Text>
              </Checkbox>
              {watch('isEncrypter') && (
                <Box mt={2} ml={100}>
                  <InvoicesSelect
                    label="Encryption Key*"
                    data={encryptionList}
                    name="encryptionKeyNo"
                  />
                </Box>
              )}
            </Box>
          )}
        </Box>
      </Flex>
    </Flex>
  );
};
