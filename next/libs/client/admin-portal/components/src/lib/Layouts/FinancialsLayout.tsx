'use client';
import { TabWrapper } from '@next/shared/ui';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { ReactNode, useEffect, useState } from 'react';

export const FinancialsLayout = ({
  children,
  tabs,
  vendorFeePeriodNo,
  defaultIndex = 0,
}: {
  children?: ReactNode;
  tabs?: any[];
  vendorFeePeriodNo?: string | null;
  defaultIndex?: number;
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const activeTab = searchParams?.get('activeTab');

  const [index, setIndex] = useState<number>(defaultIndex);

  useEffect(() => {
    if (activeTab && !isNaN(Number(activeTab))) {
      setIndex(Number(activeTab));
    }
  }, [activeTab]);

  const onChangeIndex = (newIndex: number) => {
    const newQuery: Record<string, any> = { activeTab: String(newIndex) };
    let route = `${pathname}?activeTab=${String(newIndex)}`;
    if (vendorFeePeriodNo) {
      newQuery.vendorFeePeriodNo = vendorFeePeriodNo;
      route = `${pathname}?activeTab=${String(
        newIndex
      )}&vendorFeePeriodNo=${vendorFeePeriodNo}`;
    }

    router.replace(route);
  };

  return (
    <>
      {tabs ? (
        <TabWrapper
          index={index}
          onChange={onChangeIndex}
          tabs={tabs}
          defaultIndex={defaultIndex}
        />
      ) : null}
      {children}
    </>
  );
};
