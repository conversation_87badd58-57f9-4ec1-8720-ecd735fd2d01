import { Box, Button, Flex, Input, Text } from '@chakra-ui/react';
import {
  dayOfTheMonthOptions,
  dayOfTheMonthValue,
  frequencyOptions,
  frequencyValue,
  occurenceOptions,
  weekdays,
} from '@next/admin/constants';
import { useApi } from '@next/shared/api';
import {
  createDataBodyonScheduleDrawer,
  isButtonDisbaleOnScheduleDrawer,
  setValues,
} from '@next/shared/helpers';
import { useCustomToast, useQueryParams } from '@next/shared/hooks';
import { ChakraStylesConfig, Select } from 'chakra-react-select';
import MultiSelect from 'libs/client/admin-portal/SharedComponents/MultiSelect';
import { useRouter } from 'next/navigation';
import { useEffect, useMemo } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { BiCalendarEdit, BiCalendarPlus } from 'react-icons/bi';

import GenericDrawer from './GenericDrawer';

interface ScheduleDrawerContentProps {
  onCloseHandler: () => void;
  addDrawer: any;
  isScheduleOpen?: boolean;
}

interface User {
  label: string;
  value: string | number;
}

export const ScheduleDrawerContent = ({
  onCloseHandler,
  addDrawer,
  isScheduleOpen = false,
}: ScheduleDrawerContentProps) => {
  const { register, watch, control, handleSubmit, reset, setValue } = useForm();

  const showToast = useCustomToast();

  const {
    drawer,
    drawertype,
    id,
    organizationId,
    reportSlug,
    viewtype,
    saveReportId,
  } = useQueryParams([
    'drawer',
    'drawertype',
    'viewtype',
    'id',
    'saveReportId',
    'organizationId',
    'reportSlug',
  ]);
  const { shareUser, getApi, methodApi } = useApi();

  useEffect(() => {
    if (organizationId === '' || reportSlug === '') return;
    getApi('shareUser', {
      reportSlug: reportSlug,
      orgId: organizationId,
    });

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [organizationId, reportSlug, drawertype]);

  const shareUserOptions = useMemo(
    () =>
      (shareUser || [])
        .map((item: any) => ({
          value: item.UserId,
          label: `${item.FirstName} ${item.LastName}`,
        }))
        ?.sort((a: User, b: User) => a.label.localeCompare(b.label)),

    [shareUser]
  );

  useEffect(() => {
    if (
      drawertype === 'edit' ||
      (viewtype === 'edit' && shareUserOptions?.length > 0)
    ) {
      getApi(
        'editScheduledReport',
        {
          saveReportId: parseInt(id) ? parseInt(id) : parseInt(saveReportId),
        },
        {
          onSuccess: (res) => {
            if (!res?.endSchedule) delete res?.endSchedule;

            // const scheduleDate = new Date(res?.startSchedule);
            // const todayDate = new Date(today);
            // if (scheduleDate < todayDate) {
            //   res.startSchedule = '';
            // }
            setValues(setValue, res, shareUserOptions);
          },
        }
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [drawertype, shareUserOptions]);

  const chakraStylesSelect: ChakraStylesConfig = {
    container: (provided: any) => ({
      ...provided,
      w: '100%',
    }),
    control: (provided: any) => ({
      ...provided,
      borderRadius: '6px',
    }),
    valueContainer: (provided: any) => ({
      ...provided,
      width: '160px',
    }),
    menu: (provided: any) => ({
      ...provided,
      zIndex: 999,
    }),
  };

  const today = new Date().toISOString().split('T')[0];

  const header =
    drawertype === 'add' ? (
      <Flex gap={3} alignItems={'center'}>
        <BiCalendarPlus />
        <Text color={'black'} fontWeight={'bold'}>
          Add Schedule
        </Text>
      </Flex>
    ) : (
      <Flex gap={3} alignItems={'center'}>
        <BiCalendarEdit color="black" />
        <Text color={'black'} fontWeight={'bold'}>
          Edit Schedule
        </Text>
      </Flex>
    );

  const reportName = (
    <Box>
      <Text
        pl={8}
        fontFamily={'Roboto'}
        fontSize={'14px'}
        fontStyle={'normal'}
        fontWeight={400}
        paddingTop={'0px'}
        margin={'0px'}
      >
        {addDrawer.reportName}
      </Text>
    </Box>
  );

  const formatCamelCase = (str: string): string => {
    if (!str) return '';
    return str
      .replace(/([a-z])([A-Z])/g, '$1 $2')
      .replace(/\b\w/g, (char: string) => char.toUpperCase());
  };

  const detailBody = (
    <Box
      bg="#F5F5F5"
      w="auto"
      h="60px"
      borderTop={'1px solid #D3D3D3'}
      borderBottom={'1px solid #D3D3D3'}
      paddingLeft={'24px'}
      paddingRight={'24px'}
      display="flex"
      gap={'4px'}
      marginBottom={'24px'}
    >
      <Flex
        flexDirection={'column'}
        justifyContent={'center'}
        marginRight={'32px'}
      >
        <Text
          fontFamily={'Roboto'}
          fontSize={'12px'}
          fontStyle={'normal'}
          fontWeight={700}
          textAlign={'right'}
        >
          Organization
        </Text>
        <Text
          fontFamily={'Roboto'}
          fontSize={'12px'}
          fontStyle={'normal'}
          fontWeight={700}
          textAlign={'right'}
        >
          Date of Service
        </Text>
      </Flex>
      <Flex flexDirection={'column'} justifyContent={'center'}>
        <Text
          fontFamily={'Roboto'}
          fontSize={'12px'}
          fontStyle={'normal'}
          fontWeight={400}
          whiteSpace="normal"
        >
          {formatCamelCase(addDrawer['definedFilters.organization_name'] || '')}
        </Text>
        <Text
          fontFamily={'Roboto'}
          fontSize={'12px'}
          fontStyle={'normal'}
          fontWeight={400}
        >
          {formatCamelCase(addDrawer['definedFilters.filter.label'])}
        </Text>
      </Flex>
    </Box>
  );

  const handleFormSubmission = (data: any) => {
    const dataDate = new Date(`${data?.startdate}:00:00:00`);
    const currentDate = new Date();
    currentDate.setHours(0);
    currentDate.setMinutes(0);
    currentDate.setSeconds(0);
    currentDate.setMilliseconds(0);
    if (dataDate.getTime() === currentDate.getTime()) {
      showToast({
        status: 'error',
        title: 'Report Schedule Unsuccessful',
        description:
          'Reports need to be scheduled for the future. If you need the report today, please generate.',
        position: 'top',
      });
      return;
    }
    showToast({
      status: 'success',
      title: 'Report Schedule',
      description: 'Please wait while your request is processed.',
      position: 'top',
    });
    methodApi('postScheduleReport', {
      method: 'POST',
      body: {
        saveReportId: parseInt(id) ? parseInt(id) : parseInt(saveReportId),
        ...createDataBodyonScheduleDrawer(data),
      },
      onSuccess: () => {
        showToast({
          status: 'success',
          title: 'Report Schedule',
          description: 'Report Successfully Schedule.',
          position: 'top',
        });
        reset();
        onCloseHandler();
      },
      onError: () => {
        showToast({
          status: 'error',
          title: 'Report Schedule Unsuccessful',
          description: 'There was an error while scheduling.',
          position: 'top',
        });
      },
    });
  };

  const router = useRouter();

  const handleDeleteSchedule = () => {
    methodApi('deleteScheduleReport', {
      restParams: {
        scheduleId: parseInt(id),
      },
      method: 'DELETE',
    }).then((response) => {
      showToast({
        status: 'success',
        title: 'Report deleted',
        description: 'Report Deleted Successfully',
      });
      router.push('/reports/favorites?add=true');
    });
  };

  const body = (
    <form onSubmit={handleSubmit(handleFormSubmission)}>
      <Text mb={2}>Frequency</Text>
      <Controller
        name={'frequency'}
        control={control}
        render={({ field }) => (
          <Select
            {...field}
            isClearable
            placeholder="Select Frequency"
            options={frequencyOptions}
            chakraStyles={chakraStylesSelect}
          />
        )}
      />

      {watch('frequency') ? (
        <Box mt={'16px'}>
          {watch('frequency')?.value === frequencyValue.onetime && (
            <Flex gap={'3px'} alignItems={'center'}>
              <Box>
                <Text mb={2}>Date</Text>
                <Input
                  {...register('startdate')}
                  type="date"
                  min={today}
                  max="9999-12-31"
                />
              </Box>
            </Flex>
          )}
          {watch('frequency')?.value === frequencyValue.weekly && (
            <Box mt={'16px'}>
              <Text mb={2}>Days of the Week</Text>
              <Controller
                name={'week'}
                control={control}
                render={({ field }) => (
                  <Select
                    {...field}
                    isClearable
                    placeholder="Select day of the week"
                    options={weekdays}
                    chakraStyles={chakraStylesSelect}
                    hideSelectedOptions={false}
                  />
                )}
              />
            </Box>
          )}
          {watch('frequency')?.value === frequencyValue.monthly && (
            <>
              <Box mt={'16px'}>
                <Text mb={2}>Day of the Month</Text>
                <Controller
                  name={'month'}
                  control={control}
                  render={({ field }) => (
                    <Select
                      {...field}
                      isClearable
                      placeholder="Select day of the week"
                      options={dayOfTheMonthOptions}
                      chakraStyles={chakraStylesSelect}
                      hideSelectedOptions={false}
                    />
                  )}
                />
              </Box>
              {watch('month')?.value === dayOfTheMonthValue.customDay && (
                <Flex gap={3}>
                  <Box mt={'16px'}>
                    <Text mb={2}>Occurs On</Text>
                    <Controller
                      name={'occurence'}
                      control={control}
                      render={({ field }) => (
                        <Select
                          {...field}
                          isClearable
                          options={occurenceOptions}
                          chakraStyles={chakraStylesSelect}
                          hideSelectedOptions={false}
                        />
                      )}
                    />
                  </Box>
                  <Box mt={'16px'}>
                    <Text mb={2}>Day</Text>
                    <Controller
                      name={'weekdayOccurence'}
                      control={control}
                      render={({ field }) => (
                        <Select
                          {...field}
                          isClearable
                          options={weekdays}
                          chakraStyles={chakraStylesSelect}
                          hideSelectedOptions={false}
                        />
                      )}
                    />
                  </Box>
                </Flex>
              )}
            </>
          )}
          {watch('frequency')?.value !== frequencyValue.onetime && (
            <Box mt={4}>
              <Text mb={2}>Schedule Effective Date</Text>
              <Input
                {...register('startdate')}
                type="date"
                min={today}
                max="9999-12-31"
              />
            </Box>
          )}
          <Box mt={'16px'}>
            <Text mb={2}>Subscriber(s)</Text>
            <Controller
              name={'subscriber'}
              control={control}
              render={({ field }) => (
                <MultiSelect
                  {...field}
                  isClearable
                  placeholder="Select Subscriber(s)"
                  options={shareUserOptions}
                  isMulti
                  hideSelectedOptions={false}
                  closeMenuOnSelect={false}
                />
              )}
            />
            <Box mt={'16px'}>
              <Text fontSize={14}>
                To share a report with someone associated with this
                organization, the recipient must have access to this portal and
                have the necessary permissions to view the report.
              </Text>
              <Text fontSize={14} mt={'16px'}>
                If your intended recipient is missing from this list, please
                contact your RxBenefits account manager.
              </Text>
            </Box>
          </Box>
        </Box>
      ) : null}
      <Flex mt={'16px'} gap={'10px'} justifyContent={'end'}>
        <Button
          variant={'outline'}
          borderColor={'#2A69AC'}
          color={'#2A69AC'}
          onClick={() => {
            reset();
            onCloseHandler();
          }}
          type="button"
        >
          Cancel
        </Button>
        <Button
          variant={'solid'}
          isDisabled={isButtonDisbaleOnScheduleDrawer(watch)}
          style={{
            color: 'white',
            backgroundColor: '#2A69AC',
          }}
          type="submit"
        >
          Save
        </Button>
      </Flex>
      {drawertype === 'edit' ? (
        <Box
          onClick={handleDeleteSchedule}
          mt={5}
          cursor={'pointer'}
          color={'#2A69AC'}
          float={'right'}
          fontFamily={'Roboto'}
          fontSize={'16px'}
          fontStyle={'normal'}
          fontWeight={600}
          lineHeight={'24px'}
        >
          Delete Schedule
        </Box>
      ) : (
        ''
      )}
    </form>
  );

  return (
    <GenericDrawer
      header={header}
      isOpen={drawer === 'schedule' || isScheduleOpen}
      onClose={() => {
        onCloseHandler();
        reset();
      }}
      size={'md'}
      body={body}
      reportName={reportName}
      detailBody={detailBody}
    />
  );
};
