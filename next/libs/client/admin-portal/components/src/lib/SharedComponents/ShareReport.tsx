import {
  Box,
  Button,
  Flex,
  FormControl,
  FormLabel,
  Text,
} from '@chakra-ui/react';
import { useApi } from '@next/shared/api';
import { useCustomToast, useQueryParams } from '@next/shared/hooks';
import { useEffect, useMemo, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { BiShare } from 'react-icons/bi';

import MultiSelect from '../../../../SharedComponents/MultiSelect';
import GenericDrawer from './GenericDrawer';

interface ReportModalProps {
  onClose: () => void;
  usersList: string[];
}

interface User {
  label: string;
  value: string | number;
}

const ShareReport = ({ onClose, usersList }: ReportModalProps) => {
  const { control, watch, setValue, reset } = useForm();
  const [isSharing, setIsSharing] = useState(false);

  const { getApi, methodApi, shareUser = [] } = useApi();

  const { users } = watch();

  const showToast = useCustomToast();

  const { drawer, reportSlug, organizationId, saveReportId } = useQueryParams([
    'drawer',
    'reportSlug',
    'organizationId',
    'saveReportId',
  ]);

  useEffect(() => {
    reset();
  }, [drawer, reset]);

  useEffect(() => {
    if (organizationId === '' || reportSlug === '') return;
    getApi('shareUser', {
      reportSlug: reportSlug,
      orgId: organizationId,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [organizationId, reportSlug]);

  const shareReportHandler = () => {
    if (users?.length <= 0) {
      showToast({
        title: 'Invalid User',
        description: 'Please select minimum one user to share',
        status: 'error',
        position: 'top',
      });
      return;
    }
    
    setIsSharing(true);
    methodApi('shareDownloadReport', {
      body: {
        saveReportId: parseInt(saveReportId),
        userIds: users?.map((user: any) => user.value),
      },
      onSuccess: () => {
        setIsSharing(false);
        showToast({
          title: 'Successfully shared report',
          description: 'The report has been shared.',
          status: 'success',
          position: 'top',
        });
        setValue('users', []);
        setTimeout(() => {
          onClose();
          control._reset({ users: undefined });
        }, 1500);
      },
      onError: () => {
        setIsSharing(false);
        showToast({
          title: 'Error sharing report',
          description: 'Something went wrong',
          status: 'error',
          position: 'top',
        });
      },
    });
  };

  const shareUserOptions = useMemo(
    () =>
      (shareUser || [])
        ?.map((item: any) => ({
          value: item.UserId,
          label: `${item.FirstName} ${item.LastName}`,
        }))
        ?.sort((a: User, b: User) => a.label.localeCompare(b.label)),
    [shareUser]
  );

  useEffect(() => {
    const recipientsJSON = usersList || [];
    if (!recipientsJSON.length) return;
    const recipientList = shareUserOptions.filter((key: any) =>
      recipientsJSON.includes(key?.value)
    );

    setValue('users', recipientList);
  }, [setValue, shareUserOptions, usersList]);

  const header = (
    <Flex gap={3} alignItems={'center'}>
      <BiShare color="black" />
      <Text color={'black'} fontWeight={'bold'}>
        Share Report
      </Text>
    </Flex>
  );

  const body = (
    <>
      <FormControl mb={4}>
        <FormLabel>
          <Flex justifyContent={'space-between'}>
            <Text>Users</Text>
          </Flex>
        </FormLabel>
        <Controller
          name="users"
          control={control}
          render={({ field }) => (
            <MultiSelect
              {...field}
              placeholder="Select Users from Organization"
              options={shareUserOptions}
            />
          )}
        />
      </FormControl>
      <Box mb="20px">
        <Text fontSize="14px" color="#303030">
          To share a report with someone associated with this organization, the
          recipient must have access to this portal and have the necessary
          permissions to view the report.
        </Text>
        <Text fontSize="14px" color="#303030" mt={'16px'}>
          If your intended recipient is missing from this list, please contact
          your RxBenefits account manager.
        </Text>
      </Box>
      <Flex alignItems={'center'} justifyContent={'end'} gap={2}>
        <Button
          colorScheme="blue"
          onClick={() => {
            onClose();
            control._reset({ users: undefined });
          }}
          variant={'outline'}
          style={{
            borderColor: '#0b5b8b',
            color: '#0b5b8b',
            backgroundColor: 'white',
          }}
          isDisabled={isSharing}
        >
          Cancel
        </Button>
        <Button
          style={{
            backgroundColor: '#0b5b8b',
            color: 'white',
          }}
          isDisabled={!(users?.length > 0) || isSharing}
          onClick={shareReportHandler}
        >
          {isSharing ? 'Sharing...' : 'Save'}
        </Button>
      </Flex>
    </>
  );
  return (
    <GenericDrawer
      header={header}
      body={body}
      isOpen={drawer === 'share'}
      onClose={() => {
        onClose();
        control._reset({ users: undefined });
      }}
    />
  );
};

export default ShareReport;
