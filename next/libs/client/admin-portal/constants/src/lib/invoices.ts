import { extendTheme } from '@chakra-ui/react';
import { Dispatch, SetStateAction } from 'react';

export interface invoicesProps {
  name: string;
  financialName: string;
  emailNotification: boolean;
  availabilityDistributionList: string | null | undefined;
  distribution: string;
  ftpSiteNo: string | null;
  targetDirectory: string;
  distributionList: string | undefined;
  isEncrypter: boolean;
  encryptionKeyNo: string | null;
  billToName: string;
  billToAddress1: string;
  billToAddress2: string;
  billToCity: string;
  billToState: string;
  billToZip: string;
  organizationNo: string;
  organizationName: string;
  invoiceConfigNo: string;
  ftpFileName?: string;
}

export const invoicesLabel = {
  fontSize: 12,
  color: '#06096a',
  marginBottom: 0,
  whiteSpace: 'nowrap',
  textAlign: 'right',
  width: '150px',
};

const invoicesButton = {
  fontSize: 12,
  color: 'white',
  textTransform: 'capitalize',
  backgroundColor: '#00557d',
  padding: '0px 5px',
};

export const invoiceTheme = extendTheme({
  invoicesLabel,
  invoicesButton,
});

export const DISTRIBUTION_ARRAY = [
  {
    label: 'None',
    value: '0',
  },
  {
    label: 'FTP',
    value: '1',
  },
  {
    label: 'Email',
    value: '2',
  },
  {
    label: 'FTP and Email',
    value: '3',
  },
];

export const PROCESS_STATUS = [
  {
    label: 'Active',
    value: '1',
  },
  {
    label: 'Inactive',
    value: '2',
  },
];

export const DISTRIBUTION_ARRAY_NO_EMAIL = [
  {
    label: 'None',
    value: '0',
  },
  {
    label: 'FTP',
    value: '1',
  },
];

export const CLAIM_EXPORT_SCHEMA = [
  {
    value: 'StandardInvoiceClaim2_1',
    label: 'Standard Invoice Claim Export 2.1',
  },
  {
    value: 'StandardInvoiceClaim2_0',
    label: 'Standard Invoice Claim Export 2.0',
  },
  { value: 'StandardInvoiceClaim', label: 'Standard Invoice Claim Export' },
];

export const DEFAULT_INVOICES_VALUE = {
  emailNotification: false,
  encryptionKeyNo: null,
  isEncrypter: false,
  ftpSiteNo: null,
  distribution: '0',
  availabilityDistributionList: null,
  billToName: '',
  billToAddress1: '',
  billToAddress2: '',
  billToCity: '',
  billToState: '',
  billToZip: '',
  name: '',
  invoiceConfigNo: '',
};

export interface ExportClaimData {
  claimExportConsumerNo: number | undefined;
  name: string;
  claimExportSchema: string;
  distributionType: string;
  status: string;
  connectionType: string;
  userName: string | null;
  host: string | null;
  port: number | null;
  targetFtpSiteNo: string;
  targetFtpSite: string | null;
  fileFormat: string;
  encryptionKeyNo: string | null;
  versionNumber: null | number;
  targetDirectory: string;
  file1NameParser: string;
}

export type InvoicesClaimExportPopupContentProps = {
  claimExportValue: ExportClaimData;
  changeHandler: (evt: Event) => void;
  exportClaimFileNameStatus: boolean;
  setExportClaimTargetDirectoryStatus: Dispatch<SetStateAction<boolean>>;
};

export const claimExportStateDefaultValue = {
  encryptionKeyNo: null,
  claimExportConsumerNo: undefined,
  name: '',
  claimExportSchema: '',
  distributionType: '0',
  status: '1',
  connectionType: '',
  userName: '',
  host: '',
  port: null,
  targetFtpSiteNo: '',
  targetFtpSite: '',
  fileFormat: '',
  versionNumber: null,
  targetDirectory: '',
  file1NameParser: 'NameDateTimeNoSpacesParser',
};

export type FTPSiteType = {
  ftpSiteNo: number;
  name: string;
  host: string;
  port: number;
  userName: string;
  displayValue: string | null;
};
