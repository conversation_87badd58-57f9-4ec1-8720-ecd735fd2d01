import { PERMISSIONS_ENUM } from './PermissionsBE';

export const APPagePerms: Record<string, (keyof typeof PERMISSIONS_ENUM)[]> = {
  '/admin-tools': [
    PERMISSIONS_ENUM['authorization-add-users'],
    PERMISSIONS_ENUM['authorization-view-users'],
    PERMISSIONS_ENUM['authorization-developer'],
  ],
  '/admin-tools/340b': [
    PERMISSIONS_ENUM['340B-view-records'],
    PERMISSIONS_ENUM['340B-create-records'],
  ],
  '/admin-tools/createUser': [PERMISSIONS_ENUM['authorization-add-users']],
  '/admin-tools/viewUser': [
    PERMISSIONS_ENUM['authorization-view-users'],
    PERMISSIONS_ENUM['authorization-add-users'],
  ],
  '/admin-tools/user-management': [
    PERMISSIONS_ENUM['authorization-view-users'],
  ],

  '/clients-members': [''],
  '/clients-members/files': [PERMISSIONS_ENUM['portal-view-files']],
  '/clients-members/files/[fileId]': [PERMISSIONS_ENUM['portal-view-files']],
  '/clients-members/files/[id]': [PERMISSIONS_ENUM['portal-view-files']],
  '/clients-members/members': [
    PERMISSIONS_ENUM['portal-view-members'],
    PERMISSIONS_ENUM['portal-edit-members'],
  ],
  '/clients-members/organizations': [
    PERMISSIONS_ENUM['portal-view-organizations'],
  ],
  '/clients-members/organizations/[organizationId]': [
    PERMISSIONS_ENUM['portal-view-organizations'],
  ],
  '/clients-members/organizations/1214/dashboard': [
    PERMISSIONS_ENUM['portal-view-organizations'],
  ],
  '/clients-members/organizations/1214/users': [
    PERMISSIONS_ENUM['portal-view-organization-users'],
  ],
  '/clients-members/organizations/1214/employees': [
    PERMISSIONS_ENUM['portal-view-members'],
  ],
  '/clients-members/organizations/[organizationId]/[organizationSection]': [
    PERMISSIONS_ENUM['portal-view-organizations'],
  ],
  '/clients-members/organizations/[organizationId]/eligibility-import/[screenType]':
    [PERMISSIONS_ENUM['portal-edit-eligibility']],
  '/clients-members/organizations/[organizationId]/eligibility-imports-history':
    [PERMISSIONS_ENUM['portal-edit-eligibility']],
  '/clients-members/organizations/[organizationId]/employees/[employeeId]': [
    PERMISSIONS_ENUM['portal-view-members'],
    PERMISSIONS_ENUM['portal-edit-members'],
  ],
  '/clients-members/organizations/[organizationId]/employees/[employeeId]/dependents/[dependentId]':
    [
      PERMISSIONS_ENUM['portal-view-members'],
      PERMISSIONS_ENUM['portal-edit-members'],
    ],
  '/clients-members/organizations/[organizationId]/group-table': [
    PERMISSIONS_ENUM['fms-edit-plan-fees'],
  ],
  '/clients-members/organizations/[organizationId]/importEligibility': [
    PERMISSIONS_ENUM['portal-edit-eligibility'],
  ],
  '/clients-members/organizations/[organizationId]/organization-fees-summary': [
    PERMISSIONS_ENUM['fms-edit-plan-fees'],
  ],
  '/financials/book-of-business-commission/create': [''], //'PENDING'
  '/financials/broker-commissions': [
    PERMISSIONS_ENUM['fms-broker-commissions'],
  ],
  '/financials/broker-commissions/[...id]': [
    PERMISSIONS_ENUM['fms-broker-commissions'],
  ],
  '/financials/broker-commissions/broker-entities/create': [
    PERMISSIONS_ENUM['fms-broker-commissions'],
  ],
  '/financials/broker-commissions/broker-entities/edit/[id]': [
    PERMISSIONS_ENUM['fms-broker-commissions'],
  ],
  '/financials/broker-commissions/broker-pending-retro': [
    PERMISSIONS_ENUM['fms-broker-commissions'],
  ],
  '/financials/broker-commissions/commission-holds': [
    PERMISSIONS_ENUM['fms-broker-commissions'],
  ],
  '/financials/broker-commissions/commission-gl-accounts': [
    PERMISSIONS_ENUM['fms-broker-commissions'],
  ],
  '/financials/broker-commissions/contacts/contacts': [
    PERMISSIONS_ENUM['fms-broker-commissions'],
  ],
  '/financials/broker-commissions/standard-commissions': [
    PERMISSIONS_ENUM['fms-broker-commissions'],
  ],
  '/financials/broker-commissions/statements/statements': [
    PERMISSIONS_ENUM['fms-broker-commissions'],
  ],
  '/financials/commission-statements': [
    PERMISSIONS_ENUM['fms-broker-commissions'],
    PERMISSIONS_ENUM['fms-view-commission-statements'],
  ],
  '/financials/commissions-general-ledger/[id]/view-usage': [''], //'PENDING'
  '/financials/general': [PERMISSIONS_ENUM['fms-general']],
  '/financials/general/membership-summary-detail': [
    PERMISSIONS_ENUM['fms-general'],
  ],
  '/financials/general/transaction-summary-detail': [
    PERMISSIONS_ENUM['fms-general'],
  ],
  '/financials/general/transaction-summary': [PERMISSIONS_ENUM['fms-general']],
  '/financials/invoice-adjustment-types/create': [
    PERMISSIONS_ENUM['fms-edit-invoicing'],
  ],
  '/financials/invoicing': [
    PERMISSIONS_ENUM['fms-view-invoicing'],
    PERMISSIONS_ENUM['fms-edit-invoicing'],
  ],
  '/financials/invoicing/adjustment-types': [
    PERMISSIONS_ENUM['fms-view-invoicing'],
    PERMISSIONS_ENUM['fms-edit-invoicing'],
  ],
  '/financials/invoicing/fees': [
    PERMISSIONS_ENUM['fms-view-invoicing'],
    PERMISSIONS_ENUM['fms-edit-invoicing'],
  ],
  '/financials/invoicing/import-vendor-file': [
    PERMISSIONS_ENUM['fms-view-invoicing'],
    PERMISSIONS_ENUM['fms-edit-invoicing'],
  ],
  '/financials/invoicing/reconcile': [
    PERMISSIONS_ENUM['fms-view-invoicing'],
    PERMISSIONS_ENUM['fms-edit-invoicing'],
  ],
  '/financials/invoicing/vender-fee-imports': [
    PERMISSIONS_ENUM['fms-view-invoicing'],
    PERMISSIONS_ENUM['fms-edit-invoicing'],
  ],
  '/financials/organization-commission/create': [''], //'PENDING'
  '/financials/standard-commissions/[id]/edit': [
    PERMISSIONS_ENUM['fms-broker-commissions'],
  ],
  '/financials/standard-commissions/[id]/view-usage': [
    PERMISSIONS_ENUM['fms-broker-commissions'],
  ],
  '/financials/standard-commissions/create': [
    PERMISSIONS_ENUM['fms-broker-commissions'],
  ],
  '/home': [''],
  '/integrations/eligibility-import-task-summary': [
    PERMISSIONS_ENUM['portal-edit-eligibility'],
  ],
  '/integrations/eligibility-import-task-summary/details/[organizationId]': [
    PERMISSIONS_ENUM['portal-edit-eligibility'],
  ],
  '/integrations/eligibility-imports': [
    PERMISSIONS_ENUM['portal-edit-eligibility'],
  ],
  '/integrations/eligibility-imports/[requestNo]': [
    PERMISSIONS_ENUM['portal-edit-eligibility'],
  ],
  '/integrations/eligibility-imports/[requestNo]/EIN': [
    PERMISSIONS_ENUM['portal-edit-eligibility'],
  ],
  '/integrations/eligibility-imports/[requestNo]/EIN/[recordNo]': [
    PERMISSIONS_ENUM['portal-edit-eligibility'],
  ],
  '/login': [''],
  '/pharmacy-assurance': [''],
  '/pharmacy-assurance/register': [''],
  '/protect': [PERMISSIONS_ENUM['protect-view-cds']],
  '/protect/config': [PERMISSIONS_ENUM['protect-view-cds']],
  '/protect/config/[configTab]': [PERMISSIONS_ENUM['protect-view-cds']],
  '/protect/config/conditions/[id]': [PERMISSIONS_ENUM['protect-view-cds']],
  '/protect/config/interventions/[id]': [PERMISSIONS_ENUM['protect-view-cds']],
  '/protect/cases': [PERMISSIONS_ENUM['protect-view-cds']],
  '/protect/cases/prospective/[caseNumber]': [
    PERMISSIONS_ENUM['protect-view-cds'],
  ],
  '/protect/cases/retropective/[caseNumber]': [
    PERMISSIONS_ENUM['protect-view-cds'],
  ],
  '/protect/cases/follow-up/[caseNumber]': [
    PERMISSIONS_ENUM['protect-view-cds'],
  ],
  '/reports': [PERMISSIONS_ENUM['reporting-view-reports']],
  '/reports/catalog': [PERMISSIONS_ENUM['reporting-view-reports']],
  '/reports/downloads': [PERMISSIONS_ENUM['reporting-view-reports']],
  '/reports/favorites': [PERMISSIONS_ENUM['reporting-view-reports']],
  '/reports/report/[id]': [PERMISSIONS_ENUM['reporting-view-reports']],
  '/reports/report/standard-report': [
    PERMISSIONS_ENUM['reporting-view-reports'],
  ],
  '/reports-poc': [PERMISSIONS_ENUM['reporting-view-reports']],
  '/bop': [PERMISSIONS_ENUM['bop-view']],
  '/financials/view-only-commission-statements': [
    PERMISSIONS_ENUM['fms-commission-statements-view-only'],
  ],
  '/irsv': [
    PERMISSIONS_ENUM['illuminate-view'],
    PERMISSIONS_ENUM['illuminate-edit'],
    PERMISSIONS_ENUM['authorization-developer'],
  ],
};
