import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

type OnboardingState = {
  needsPrivacyReacceptance: boolean;
  firstLogin: boolean;
  setNeedsPrivacyReacceptance: (value: boolean) => void;
  setFirstLogin: (value: boolean) => void;
  resetOnboardingState: () => void;
};

export const useOnboardingStore = create<OnboardingState>()(
  persist(
    (set) => ({
      needsPrivacyReacceptance: false,
      firstLogin: true, // Default to true on first load

      setNeedsPrivacyReacceptance: (value: boolean) =>
        set({ needsPrivacyReacceptance: value }),

      setFirstLogin: (value: boolean) => set({ firstLogin: value }),

      resetOnboardingState: () =>
        set({ needsPrivacyReacceptance: false, firstLogin: false }),
    }),
    {
      name: 'onboarding-storage',
      storage: createJSONStorage(() => localStorage),
    }
  )
);
