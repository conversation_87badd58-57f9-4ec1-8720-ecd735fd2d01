import {
  Box,
  Image,
  <PERSON>u,
  <PERSON>u<PERSON>utton,
  <PERSON>u<PERSON><PERSON>,
  Menu<PERSON>ist,
  <PERSON>dal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalHeader,
  ModalOverlay,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import {
  PORTAL_VIDEO_URL,
  RXBUDDY_ANIMATION,
  STATIC_RXBuddy_LOGO_URL,
} from '@next/shared/constants';
import { AdminUserContext } from '@next/shared/contexts';
import { useContext, useEffect, useState } from 'react';
import Joyride from 'react-joyride';

const RXBuddy = ({
  isProtect,
  newLogin,
}: {
  isProtect: any;
  organizationNo: any;
  employeeNo: any;
  dependentNo: any;
  newLogin: any;
}) => {
  const { userState } = useContext(AdminUserContext);
  const isNew = userState[`https://qa.rxbenefits.cloud/is_new`];
  const [showTour, setShowTour] = useState(false);
  const [animationFinished, setAnimationFinished] = useState(false);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [tourStep, setTourStep] = useState(0);
  const dateOfBirth = userState?.metaData?.data?.[0].birthdate;

  useEffect(() => {
    if (showTour || isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [showTour, isOpen]);

  const handleAnimationEnd = () => {
    setAnimationFinished(true);
  };

  function calculateAge(dateOfBirth: any) {
    const dob = new Date(dateOfBirth);
    const today = new Date();

    const age = today.getFullYear() - dob.getFullYear();

    const isUnder18 =
      age < 18 ||
      (age === 18 && today.getMonth() < dob.getMonth()) ||
      (age === 18 &&
        today.getMonth() === dob.getMonth() &&
        today.getDate() < dob.getDate());
    return isUnder18;
  }

  const isUnder18 = calculateAge(dateOfBirth);

  useEffect(() => {
    if (isNew) {
      setTimeout(() => {
        setShowTour(true);
      }, 2000);
    }
  }, [isNew]);

  const handleTourEnd = (data: any) => {
    if (!data) {
      setShowTour(false);
      setTourStep(0);
      return;
    }
    const { status } = data;
    if (!data) {
      setShowTour(false);
      setTourStep(0);
    }
    if (data.action === 'close') {
      setShowTour(false);
    }
    if (data.type === 'step:after') {
      if (data.action === 'next') {
        setTourStep(data.index + 1);
      }

      if (data.action === 'prev') {
        setTourStep(data.index - 1);
      }
    }

    if (status === 'finished' || status === 'skipped') {
      onOpen();
    }
  };

  const restartTour = (index: number) => {
    setTourStep(index); // Jump to the 3rd step (index 2)
    setShowTour(true); // Start the tour
  };

  // Define the Joyride steps for onboarding
  const steps = [
    {
      name: 'ID Card',
      target: '.id-card',
      content: (
        <Box textAlign="left">
          <Box fontWeight={700} mb={1}>
            ID Card
          </Box>
          <Box>View, download, or email a copy of your ID card.</Box>
        </Box>
      ),
      disableBeacon: true,
      placement: 'right-start',
      offset: -10,
    },
    ...(isProtect
      ? [
          {
            name: 'Prior Authorizations',
            target: '.prior-auth',
            disableBeacon: true,
            placement: 'left-start',
            offset: -10,
            content: (
              <Box textAlign="left">
                <Box fontWeight={700} mb={1}>
                  Prior Authorizations
                </Box>
                <Box>
                  Status of your medications that require approval for coverage
                  by your pharmacy benefits plan.
                </Box>
              </Box>
            ),
          },
        ]
      : []),
    {
      name: 'Claims',
      target: '.prior-claims',
      placement: 'left-start',
      offset: -10,
      content: (
        <Box textAlign="left">
          <Box fontWeight={700} mb={1}>
            Claims
          </Box>
          <Box>
            Your claims history for the past 18 months, including any claims for
            eligible dependents.
          </Box>
        </Box>
      ),
    },
    {
      name: 'Additional Pharmacy Benefits Information',
      target: '.pbm',
      disableBeacon: true,
      placement: 'right-start',
      offset: -10,
      content: (
        <Box textAlign="left">
          <Box fontWeight={700} mb={1}>
            Additional Pharmacy Benefits Information
          </Box>
          <Box>
            Check your prescription cost, manage your refills, and verify your
            pharmacy delivery service.
          </Box>
        </Box>
      ),
    },
    {
      name: 'Plan Details',
      target: '.coverage',
      disableBeacon: true,
      placement: 'right-start',
      offset: -10,
      content: (
        <Box textAlign="left">
          <Box fontWeight={700} mb={1}>
            Plan Details
          </Box>
          <Box>A full description of your pharmacy benefits plan.</Box>
        </Box>
      ),
    },
    {
      name: 'Security Settings',
      target: '.security-settings',
      disableBeacon: true,
      placement: 'right-start',
      offset: -10,
      content: (
        <Box textAlign="left" mb={1}>
          <Box fontWeight={700}>Security Settings</Box>
          <Box>Choose how you want to protect your account information.</Box>
        </Box>
      ),
    },
    {
      name: 'Communication Preferences',
      target: '.communication-preferences',
      disableBeacon: true,
      placement: 'right-start',
      offset: -10,
      content: (
        <Box textAlign="left" mb={1}>
          <Box fontWeight={700}>Communication Preferences</Box>
          <Box>Confirm how you want to receive prescription updates.</Box>
        </Box>
      ),
    },
    {
      name: 'Document Library',
      target: '.document-library',
      disableBeacon: true,
      placement: 'right-start',
      offset: -10,
      content: (
        <Box textAlign="left" mb={1}>
          <Box fontWeight={700}>Document Library</Box>
          <Box>
            Important pharmacy benefits forms, including Prior Authorization
            Requests.
          </Box>
        </Box>
      ),
    },
    {
      name: 'Glossary of Terms',
      target: '.glossary',
      disableBeacon: true,
      placement: 'right-start',
      offset: -10,
      content: (
        <Box textAlign="left" mb={1}>
          <Box fontWeight={700}>Glossary of Terms</Box>
          <Box>
            Pharmacy benefits terminology to help you understand your coverage
            plan.
          </Box>
        </Box>
      ),
    },
    {
      name: 'Contact Us',
      target: '.contact-us',
      disableBeacon: true,
      placement: 'right-start',
      offset: -10,
      content: (
        <Box textAlign="left" mb={1}>
          <Box fontWeight={700}>Contact Us</Box>
          <Box>How to reach member services.</Box>
        </Box>
      ),
    },
    ...(!isUnder18
      ? [
          {
            name: 'Appointed Representatives',
            target: '.aor',
            disableBeacon: true,
            placement: 'right-start',
            offset: -10,
            content: (
              <Box textAlign="left" mb={1}>
                <Box fontWeight={700}>Appointed Representatives</Box>
                <Box>
                  View or update the individuals listed as appointed
                  representatives for your pharmacy benefits.
                </Box>
              </Box>
            ),
          },
        ]
      : []),
  ] as any;

  return (
    <Box>
      <Box float="right">
        <Menu>
          <MenuButton>
            {!animationFinished && newLogin ? (
              <Box h={['32px', '50px']} mr=".5em" mt={['.4em', '.45em']}>
                <video
                  autoPlay
                  muted
                  onEnded={handleAnimationEnd}
                  playsInline
                  webkit-playsinline
                  controls={false}
                  style={{
                    objectFit: 'cover',
                    maxWidth: '100%',
                    maxHeight: '100%',
                  }}
                >
                  <source src={RXBUDDY_ANIMATION} type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
              </Box>
            ) : (
              <Image
                src={STATIC_RXBuddy_LOGO_URL}
                alt="RXBuddy Logo"
                h={['32px', '50px']}
                mr=".5em"
                mt={['.4em', '.45em']}
              />
            )}
          </MenuButton>

          <MenuList
            p="1em"
            border="9px solid #7BCC03"
            zIndex={6}
            fontSize="14px"
          >
            <Text fontWeight={700}>
              Learn more about your My RxBenefits portal
            </Text>
            <Text mb="8px">Select any topic to learn more</Text>
            {steps.map((step: any, i: number) => {
              return (
                <MenuItem
                  border="1px solid #008750"
                  borderRadius={10}
                  _hover={{ background: 'none' }}
                  _focus={{ background: 'none' }}
                  mb={2}
                  onClick={() => restartTour(i)}
                  color="#008750"
                  key={i}
                  justifyContent="center"
                >
                  {step.name}
                </MenuItem>
              );
            })}
            <MenuItem
              border="1px solid #008750"
              borderRadius={10}
              _hover={{ background: 'white' }}
              mb={2}
              onClick={onOpen}
              color="#008750"
              justifyContent="center"
            >
              Member Video
            </MenuItem>
          </MenuList>
        </Menu>
      </Box>
      {showTour && (
        <Joyride
          locale={{
            back: 'Previous',
            close: 'Close',
            last: 'Next', // Custom text for the last button
            next: 'Next',
            skip: 'Skip',
          }}
          steps={steps}
          run={showTour} // Controls whether the tour is running
          stepIndex={tourStep} // Control the starting step (for jumping)
          continuous
          scrollOffset={275}
          hideCloseButton={false}
          callback={handleTourEnd} // Handle when the tour ends or steps change
          styles={{
            options: {
              arrowColor: 'transparent',
              backgroundColor: '#e3ffeb',
              overlayColor: 'rgba(0,0,0, 0.5)',
              primaryColor: '#000',
              textColor: 'black',
              zIndex: 1000,
            },
            tooltip: {
              backgroundColor: 'white',
              border: '9px solid #7BCC03',
              borderRadius: '8px',
              position: 'relative',
              padding: '5px 10px 15px 5px',
              zIndex: 10000,
              width: '240px',
            },
            tooltipContent: {
              padding: '5px',
            },
            buttonNext: {
              backgroundColor: '#008750',
              border: '1px solid #008750',
              borderRadius: '8px',
            },
            buttonBack: {
              border: '1px solid #008750',
              borderRadius: '8px',
              color: '#008750',
              position: 'absolute',
              left: 10,
            },
          }}
        />
      )}
      <Modal
        isOpen={isOpen}
        onClose={() => {
          handleTourEnd(null);
          onClose();
        }}
        size="xl"
      >
        <ModalOverlay />
        <ModalContent mt={40}>
          <ModalHeader fontWeight={500} fontSize="medium">
            Welcome to RxBenefits!
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <video
              autoPlay
              controls
              controlsList="nodownload"
              width="100%"
              height="auto"
            >
              <source src={PORTAL_VIDEO_URL} type="video/mp4" />
              Your browser does not support the video tag.
            </video>
          </ModalBody>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default RXBuddy;
