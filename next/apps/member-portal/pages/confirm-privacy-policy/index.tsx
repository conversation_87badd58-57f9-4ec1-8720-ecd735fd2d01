import { Box, Button, Checkbox, Heading, Image, Text } from '@chakra-ui/react';
import { useApi } from '@next/shared/api';
import { AdminUserContext } from '@next/shared/contexts';
import PrivacyPolicyIcon from 'apps/member-portal/components/CustomIcons/PrivacyPolicyIcon';
import { useMemberData } from 'apps/member-portal/hooks/useMemberData';
import { formatDate } from 'apps/member-portal/utils';
import { useRouter } from 'next/router';
import React, { useContext, useEffect, useState } from 'react';
import { FaArrowUpRightFromSquare } from 'react-icons/fa6';
const ConfirmPrivacyPolicy = () => {
  const router = useRouter();
  const { methodApi } = useApi();
  const { setMissingCommPrefIndicator } = useContext(AdminUserContext);
  const [termsAccepted, setTermsAccepted] = useState(false);
  const {
    firstName,
    lastName,
    organizationNo,
    employeeNo,
    dependentNo,
    isDependent,
    isAdmin,
  } = useMemberData();

  useEffect(() => {
    if (organizationNo && employeeNo) {
      methodApi('memberGetCommunicationPreferences', {
        method: 'POST',
        body: {
          organizationNo,
          employeeNo,
          dependentNo,
        },
        onSuccess: (res) => {
          console.log(res);
        },
        onError: () => {
          console.warn('Failed to fetch communication preferences');
        },
      });
    }
  }, [organizationNo, employeeNo, dependentNo, methodApi]);

  const logout = () => {
    router.push('/api/auth/logout');
  };

  const handleUpdate = () => {
    if (isAdmin) {
      router.push('/home');
      return;
    }

    methodApi('memberUpdateCommunicationPreferences', {
      method: 'POST',
      body: {
        eventName: 'update',
        source: 'MemberPortal',
        memberId: 'yes',
        firstName,
        lastName,
        organizationNo,
        employeeNo,
        dependentNo,
        updatedBy: isDependent ? dependentNo.toString() : employeeNo.toString(),
        privacypolicyacknowledgeddate: new Date(),
      },
      onSuccess: (res) => {
        router.push('/home');
      },
      onError: (res) => {
        console.log('error');
      },
    });
  };

  return (
    <>
      <Box
        bg="white"
        position="absolute"
        minH="100vh"
        left={0}
        right={0}
        top={0}
        pt={40}
        px={4}
      >
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          color="gray.600"
          padding="40px"
        >
          <PrivacyPolicyIcon />{' '}
        </Box>
        <Box textAlign="center" color="gray.600">
          <Heading>
            <Text fontSize="18px">Review and Accept Terms</Text>
          </Heading>
        </Box>

        <Box textAlign="center" mt={6} color="gray.600" gap="-3px">
          <Text>
            Before continuing, you must review and agree to <br /> the
            RxBenefits{' '}
            <span style={{ display: 'inline-block', verticalAlign: 'middle' }}>
              <FaArrowUpRightFromSquare />
            </span>{' '}
            <a
              href="https://www.rxbenefits.com/privacy-policy/terms-of-use/"
              target="_blank"
              rel="noopener noreferrer"
              style={{ textDecoration: 'underline' }}
            >
              Terms of Use
            </a>{' '}
            and{' '}
            <span style={{ display: 'inline-block', verticalAlign: 'middle' }}>
              <FaArrowUpRightFromSquare />
            </span>{' '}
            <a
              href="https://www.rxbenefits.com/privacy-policy/"
              target="_blank"
              rel="noopener noreferrer"
              style={{ textDecoration: 'underline' }}
            >
              Privacy Policy
            </a>
            .
          </Text>
        </Box>

        <Box maxW="480px" mx="auto" py={6} bg="white" color="gray.600">
          <Box p={2}>
            <Checkbox
              size="md"
              onChange={(e) => setTermsAccepted(!termsAccepted)}
            >
              I have read and agree to the Terms of Use and Privacy Policy
            </Checkbox>
          </Box>
          <Box
            display="flex"
            justifyContent="flex-end"
            mt={4}
            minH="50vh"
            gap={2}
          >
            <Button bg="none" onClick={logout}>
              Decline and Logout
            </Button>
            <Button
              variant="solid"
              colorScheme="green"
              w={['full', 'auto']}
              isDisabled={!termsAccepted}
              onClick={handleUpdate}
            >
              Accept and Continue
            </Button>
          </Box>
        </Box>
      </Box>
      <Box bg="white" minH="90vh"></Box>
    </>
  );
};

export default ConfirmPrivacyPolicy;
