import { Box, Grid, GridItem, Hide, Show } from '@chakra-ui/react';
import { useApi } from '@next/shared/api';
import { HomeAlertDisplay } from 'apps/member-portal/components/HomeAlertDisplay';
import { IdCard } from 'apps/member-portal/components/IDCard';
import { MemberResources } from 'apps/member-portal/components/MemberResources';
import PBMSSO from 'apps/member-portal/components/PBMSSO';
import { Coverage } from 'apps/member-portal/components/PlanDetails';
import { PriorAuth } from 'apps/member-portal/components/PriorAuth';
import { PriorClaims } from 'apps/member-portal/components/PriorClaims';
import WelcomeMessage from 'apps/member-portal/components/WelcomeMessage';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

import { useMemberData } from '../../hooks/useMemberData';
import { useImpersonationStore } from '../../stores/useImpersonationStore';
import { useOnboardingStore } from 'apps/member-portal/stores/useOnboardingStore';

function shouldRedirectToOnboarding({
  data,
  birthdate,
}: {
  data: any;
  birthdate: string;
}): boolean {
  const hasConfirmed = !!data?.memberconfirmeddate?.trim();
  const hasValidContactInfo =
    data?.emailAddress !== '<EMAIL>' &&
    data?.phoneNumber !== '0000000000';

  const birthDateObj = new Date(birthdate);
  const age = isNaN(birthDateObj.getTime())
    ? 0
    : new Date().getFullYear() - birthDateObj.getFullYear();
  const isAdult = age >= 18;

  const { setFirstLogin } = useOnboardingStore.getState();

  if (!hasConfirmed) {
    setFirstLogin(true);
    return true;
  }

  setFirstLogin(false);

  if (isAdult && !hasValidContactInfo) return true;

  return false;
}

const Home = () => {
  const router = useRouter();
  const {
    firstName,
    lastName,
    middleInitial,
    birthdate: dateOfBirth,
    dependentNo,
    employeeNo,
    organizationNo,
    isAdmin,
    impMember,
  } = useMemberData();

  const [loading, setLoading] = useState(true);

  const memberName = middleInitial
    ? `${firstName} ${middleInitial} ${lastName}`.toLowerCase()
    : `${firstName} ${lastName}`.toLowerCase();

  const {
    getActiveCoverage,
    getTempIdInfo = null,
    getPBC,
    getIsProtect,
    getApi,
    methodApi,
  } = useApi();

  const { impCommunicationComplete } = useImpersonationStore();

  function shouldShowPrivacyPolicy(dateValue: string): boolean {
    if (!dateValue) return true; // Case a: Empty string or falsy value

    const parsedDate = new Date(dateValue);

    // Invalid date check
    if (isNaN(parsedDate.getTime())) return true;

    const now = new Date();
    const daysDifference =
      (now.getTime() - parsedDate.getTime()) / (1000 * 60 * 60 * 24);

    return daysDifference > 180;
  }

  useEffect(() => {
    if (isAdmin && !impMember) {
      router.push('/admin/impersonation-tool');
    }
  }, [isAdmin, impMember, router]);

  useEffect(() => {
    if (organizationNo && employeeNo) {
      getApi('getActiveCoverage', { orgId: organizationNo, employeeNo });
      getApi('getPBC', { employeeNo, dependentNo });
      getApi('getIsProtect', { orgId: organizationNo });
      getApi('getTempIdInfo', { orgId: organizationNo, employeeNo });

      methodApi('memberGetCommunicationPreferences', {
        method: 'POST',
        body: {
          organizationNo,
          employeeNo,
          dependentNo,
        },
        onSuccess: (res) => {
          const data = res.data;
          const needsOnboarding = shouldRedirectToOnboarding({
            data,
            birthdate: dateOfBirth,
          });
          const needsTermsReacceptance = shouldShowPrivacyPolicy(
            data.privacypolicyacknowledgeddate
          );

          const { setNeedsPrivacyReacceptance } = useOnboardingStore.getState();

          if (isAdmin && impCommunicationComplete) {
            setLoading(false);
            return;
          }

          if (needsOnboarding) {
            // Set the flag so onboarding page knows to show the privacy policy after onboarding
            setNeedsPrivacyReacceptance(needsTermsReacceptance);
            router.push('/onboarding/communication-preferences');
            return;
          }

          if (needsTermsReacceptance) {
            // User doesn't need onboarding but does need to accept the privacy policy
            setNeedsPrivacyReacceptance(true);
            router.push('/confirm-privacy-policy');
            return;
          }

          // No further action needed
          setNeedsPrivacyReacceptance(false); // Clear any previous state just to be safe
          setLoading(false);
        },
      });
    }
  }, [
    organizationNo,
    employeeNo,
    dependentNo,
    dateOfBirth,
    methodApi,
    getApi,
    router,
    isAdmin,
    impCommunicationComplete,
  ]);

  if (loading) return null;

  return (
    <Box>
      <HomeAlertDisplay />
      <WelcomeMessage name={memberName} />
      <Grid
        templateColumns={[
          'repeat(1, 1fr)',
          'repeat(1, 1fr)',
          'repeat(1, 1fr)',
          'repeat(1, 1fr)',
          'repeat(6, 1fr)',
        ]}
        gap={[0, 0, 0, 0, 6]}
      >
        <GridItem colSpan={[6, 6, 2, 2]}>
          {getTempIdInfo?.pbmIdentifier && (
            <IdCard
              orgId={organizationNo}
              memberId={employeeNo}
              memberName={memberName}
              className="id-card"
            />
          )}
          <PBMSSO orgId={organizationNo} memberId={employeeNo} />
          <Hide below="lg">
            <Coverage
              memberName={memberName}
              coverage={getActiveCoverage}
              pbcData={getPBC}
              className="coverage"
            />
            <MemberResources
              tempId={getTempIdInfo}
              isProtect={getIsProtect?.protectClient}
            />
          </Hide>
        </GridItem>

        <GridItem colSpan={[6, 6, 4, 4]}>
          {getIsProtect?.protectClient && <PriorAuth className="prior-auth" />}
          <PriorClaims className="prior-claims" />
          <Show below="lg">
            <Coverage
              memberName={memberName}
              coverage={getActiveCoverage}
              pbcData={getPBC}
              className="coverage"
            />
            <MemberResources
              tempId={getTempIdInfo}
              isProtect={getIsProtect?.protectClient}
            />
          </Show>
        </GridItem>
      </Grid>
    </Box>
  );
};

export default Home;
