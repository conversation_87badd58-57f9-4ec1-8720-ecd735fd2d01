import { Box, Button, Center, Flex, Spinner, Text } from '@chakra-ui/react';
import { DateFilter } from '@next/admin/components';
import { CustomDateFilter } from '@next/admin/components';
import { useApi } from '@next/shared/api';
import { isButtonDisabledSingleReport } from '@next/shared/helpers';
import { useCustomToast, useQueryParams } from '@next/shared/hooks';
import { ChakraStylesConfig, Select } from 'chakra-react-select';
import {
  ActionMenu,
  ConfirmationModal,
} from 'libs/client/admin-portal/components/src/lib/SharedComponents';
import ReportModal from 'libs/client/admin-portal/components/src/lib/SharedComponents/ReportModal';
import { ScheduleDrawerContent } from 'libs/client/admin-portal/components/src/lib/SharedComponents/ScheduleDrawerContent';
import SideScrollTable from 'libs/client/admin-portal/components/src/lib/SharedComponents/SideScrollTable';
import { generateMonthOptions } from 'libs/client/admin-portal/components/src/lib/standardReport/EligibilityReportsFilter/utils';
import MultiSelect from 'libs/client/admin-portal/SharedComponents/MultiSelect';
import { useRouter, useSearchParams } from 'next/navigation';
import React from 'react';
import { useEffect, useMemo, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { FaEdit, FaRegCalendarPlus, FaRegEye } from 'react-icons/fa';
import { IoMdStarOutline } from 'react-icons/io';
import { LuRefreshCcwDot } from 'react-icons/lu';
import { MdArrowBack } from 'react-icons/md';
import { RiDeleteBinLine } from 'react-icons/ri';

const SingleReportFavourite = () => {
  const {
    organizationId,
    saveReportId,
    reportId,
    viewtype,
    title: noFormatTitle,
    desc: noFormatDesc,
    orgName: noFormatOrgName,
  } = useQueryParams([
    'organizationId',
    'saveReportId',
    'viewtype',
    'reportId',
    'title',
    'desc',
    'orgName',
  ]);
  const orgName = decodeURIComponent(noFormatOrgName);
  const method = useForm<any>({
    defaultValues: {
      orgID: { label: orgName, value: +organizationId },
    },
  });
  const { control, watch, reset, setValue, setError, clearErrors, getValues } =
    method;

  const [currentData, setCurrentData] = useState<any>({});
  const [spinner, setSpinner] = useState(false);
  const btnRef = React.useRef<HTMLButtonElement>(null);

  const [menuOpenState, setMenuOpenState] = useState<{ [k: string]: any }>([]);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [isScheduleOpen, setScheduleOpen] = useState<boolean>(false);
  const [scheduleData, setScheduleData] = useState({});

  const handleCloseModal = () => setIsModalOpen(false);
  const handleConfirm = () => {
    resetSubscribers();
    saveConfiguration();
    handleCloseModal();
  };

  const { orgID, dateOfService, groupNames } = watch();
  const [modalState, setmodalState] = useState<{
    modal: boolean;
    title: string;
    desc: string;
    isEdit?: boolean;
  }>({
    modal: false,
    title: '',
    desc: '',
    isEdit: false,
  });

  const title = decodeURIComponent(noFormatTitle);
  const desc = decodeURIComponent(noFormatDesc);

  const router = useRouter();
  const searchParams = useSearchParams();

  enum FilterNames {
    'customDateRange' = 'Custom Date Range',
    'benefitPeriod' = 'Current Benefit Period',
    'yearToDate' = 'Year to Date',
    'lastCompletedMonth' = 'Last Completed Month',
    'rolling12' = 'Rolling 12',
    'lastCompletedQuarter' = 'Last Completed Quarter',
    'offCycle' = 'Off Cycle',
  }

  const {
    getApi,
    organizations,
    reportingEligiblitySummaryFilters = [],
    saveReportById = [],
    methodApi,
    organizationGroups = [],
    reportsList,
    getFavourites,
    editScheduledReport,
  } = useApi(
    [
      'organizations',
      'reportingEligiblitySummaryFilters',
      'organizationGroups',
      'saveReportById',
      'getFavourites',
      'reportsList',
      'editScheduledReport',
    ],
    {
      orgId: organizationId,
      orgOffset: 0,
      orgName: '',
      reportID: reportId,
      saveReportID: saveReportId,
      saveReportId: saveReportId,
    }
  );

  const parentReportName = useMemo(() => {
    if (!reportsList || !reportsList.data) return [];
    return reportsList.data.filter(
      (key: any) => +key?.reportid === +reportId
    )?.[0]?.reportName;
  }, [reportsList, reportId]);

  const showToast = useCustomToast();
  const handleMenuOpen = (menuKey: string, isOpen: boolean) => {
    setMenuOpenState((prevState) => ({
      ...prevState,
      [menuKey]: isOpen,
    }));
  };

  const filteringList = useMemo(() => {
    if (organizations?.data) {
      const value = organizations?.data.map((item: Record<string, any>) => ({
        label: item?.name,
        value: item?.organizationNo?.toString(),
      }));
      return [...value];
    } else return [];
  }, [organizations]);

  const groupsOptions = useMemo(() => {
    if (organizationGroups?.length > 0) {
      return organizationGroups.map((item: Record<string, any>) => ({
        label: item.name,
        value: item.benefitGroupNo,
      }));
    } else return [];
  }, [organizationGroups]);

  const chakraStyles: ChakraStylesConfig = {
    container: (provided: any) => ({
      ...provided,
      w: '100%',
    }),
    control: (provided: any) => ({
      ...provided,
      borderRadius: '6px',
    }),
  };

  const saveConfiguration = () => {
    const {
      smonth,
      emonth,
      syear,
      eyear,
      adjsmonth,
      adjsyear,
      adjemonth,
      adjeyear,
    } = dateProcess();

    const { groupNames } = watch();
    const grpIds = groupNames?.map((each: any) => each?.value);
    methodApi('editFavouriteConfiguration', {
      method: 'PUT',
      body: {
        saveReportId: Number(saveReportId),
        configuration: {
          organization_number: Number(orgID?.value),
          organization_name: orgID?.label,
          filter: {
            label: dateOfService?.value,
            startMonth: Number(smonth),
            startYear: Number(syear),
            endMonth: Number(emonth),
            endYear: Number(eyear),
          },
          groups: grpIds,
        },
        groups: grpIds,
      },
    });

    const body = {
      saveReportId: Number(saveReportId),
      configuration: {
        organization_number: Number(orgID?.value),
        organization_name: orgID?.label,
        filter: {
          adjudicationRange: {
            startMonth: adjsmonth,
            startYear: adjsyear,
            endMonth: adjemonth,
            endYear: adjeyear,
          },
          label: dateOfService?.value,
          startMonth: Number(smonth),
          startYear: Number(syear),
          endMonth: Number(emonth),
          endYear: Number(eyear),
        },
        groups: grpIds,
      },
    };

    if (parentReportName === 'Lag Report') {
      body.configuration.filter.adjudicationRange = {
        startMonth: adjsmonth,
        startYear: adjsyear,
        endMonth: adjemonth,
        endYear: adjeyear,
      };
    }

    methodApi('editFavouriteConfiguration', {
      method: 'PUT',
      body: body,
      onSuccess(res) {
        showToast({
          title: 'Configuration Edited successfully',
          status: 'success',
          position: 'top',
        });
        router.push('/reports/favorites');
      },
      onError: () => {
        showToast({
          status: 'error',
          title: 'Configuration edit unsuccessful',
          position: 'top',
        });
      },
    });
  };

  const resetSubscribers = () => {
    const data = editScheduledReport;
    methodApi('postScheduleReport', {
      method: 'POST',
      body: {
        saveReportId: Number(saveReportId),
        frequency: data?.frequency,
        startSchedule: data?.startSchedule,
        dayOfMonth: data?.dayOfMonth,
        dayOfWeek: data?.dayOfWeek,
        occursOn: data?.occursOn,
        OccursOnDay: data?.occursOnDay,
        subscriber: [],
      },
      onSuccess: () => {
        showToast({
          status: 'success',
          title: 'Subscribers Reset Successful',
          description: 'Subscribers Reset to default.',
          position: 'top',
        });
      },
      onError: () => {
        showToast({
          status: 'error',
          title: 'Subscribers Reset Unsuccessful',
          description: 'There was an error while scheduling.',
          position: 'top',
        });
      },
    });
  };

  const handleConfiguration = () => {
    const prevOrg = orgName.trim();
    const currentOrg = getValues('orgID');
    const trimedCurrentOrg = currentOrg?.label.trim();
    if (saveReportById?.data[0]?.scheduleDate !== '') {
      if (
        prevOrg !== trimedCurrentOrg &&
        editScheduledReport?.subscriber?.length > 0
      ) {
        setIsModalOpen(true);
        return;
      }
    }
    saveConfiguration();
  };

  const dateProcess = () => {
    let smonth, emonth, syear, eyear, adjsmonth, adjsyear, adjemonth, adjeyear;
    const {
      dateOfService,
      startMonth,
      endMonth,
      startYear,
      endYear,
      startDate,
      endDate,
      startMonthAdj,
      startYearAdj,
      endMonthAdj,
      endYearAdj,
    } = watch();

    if (dateOfService?.value === 'customDateRange') {
      smonth = startMonth?.value;
      emonth = endMonth?.value;
      syear = startYear?.value;
      eyear = endYear?.value;
    } else if (dateOfService.value === 'offCycle') {
      const currentDate = new Date();
      const currentMonth = currentDate.getMonth() + 1;
      const currentYear = currentDate.getFullYear();
      smonth = parseInt(startMonth?.value);
      syear = parseInt(startYear?.value);
      if (
        syear > currentYear ||
        (syear === currentYear && smonth > currentMonth)
      ) {
        emonth = currentMonth;
        eyear = currentYear;
      } else {
        emonth = (smonth + 11) % 12 || 12;
        eyear = smonth === 1 ? syear : syear + 1;

        if (
          eyear > currentYear ||
          (eyear === currentYear && emonth > currentMonth)
        ) {
          emonth = currentMonth;
          eyear = currentYear;
        }
      }
    } else if (
      dateOfService.value === 'benefitPeriod' ||
      dateOfService.value === 'yearToDate' ||
      dateOfService.value === 'lastCompletedQuarter' ||
      dateOfService.value === 'lastCompletedMonth' ||
      dateOfService.value === 'rolling12'
    ) {
      smonth = startDate.split('/')[0];
      syear = startDate.split('/')[1];
      emonth = endDate.split('/')[0];
      eyear = endDate.split('/')[1];
    }
    if (saveReportById?.data?.[0].definedFilters?.filter?.adjudicationRange) {
      adjsmonth = Number(startMonthAdj?.value);
      adjsyear = Number(startYearAdj?.value);
      adjemonth = Number(endMonthAdj?.value);
      adjeyear = Number(endYearAdj?.value);
    }
    return {
      smonth,
      emonth,
      syear,
      eyear,
      adjsmonth,
      adjsyear,
      adjemonth,
      adjeyear,
    };
  };

  const handleReport = () => {
    setSpinner(true);
    setCurrentData([]);

    if (orgID) {
      const {
        smonth,
        emonth,
        syear,
        eyear,
        adjsmonth,
        adjsyear,
        adjemonth,
        adjeyear,
      } = dateProcess();

      const body: any = {
        organization_number: +orgID?.value,
        organization_name: orgID?.label,
        filter: {
          label: dateOfService?.value,
          startMonth: parseInt(smonth as string),
          startYear: parseInt(syear as string),
          endMonth: parseInt(emonth as string),
          endYear: parseInt(eyear as string),
        },
        groups: groupNames?.map((item: any) => item?.value) || [],
      };

      if (parentReportName === 'Lag Report') {
        body.filter.adjudicationRange = {
          startMonth: adjsmonth,
          startYear: adjsyear,
          endMonth: adjemonth,
          endYear: adjeyear,
        };
      }
      clearErrors(['date_of_service_month', 'orgId']);
      methodApi('runReport', {
        method: 'POST',
        restParams: { reportId: reportId },
        body: body,
        onSuccess: (data) => {
          setSpinner(false);
          setCurrentData(data ? data : []);
        },
        onError: () => {
          setSpinner(false);
        },
      });
    } else {
      if (orgID) {
        setError('orgId', {
          type: 'custom',
          message: 'this is required',
        });
      }
    }
  };

  const downloadSheetReport = (orgID: any) => {
    setSpinner(true);
    const {
      smonth,
      emonth,
      syear,
      eyear,
      adjsmonth,
      adjsyear,
      adjemonth,
      adjeyear,
    } = dateProcess();
    // shadowed Org ID some state issue.

    const currentFormValues = getValues();
    const currentDateOfService = currentFormValues.dateOfService;

    const payload: any = {
      organization_number: +orgID.value,
      organization_name: orgID.label,
      filter: {
        label: currentDateOfService?.value,
        startMonth: parseInt(smonth as string),
        startYear: parseInt(syear as string),
        endMonth: parseInt(emonth as string),
        endYear: parseInt(eyear as string),
      },
      groups: groupNames?.map((item: any) => item?.value) || [],
    };

    if (parentReportName === 'Lag Report') {
      payload.filter.adjudicationRange = {
        startMonth: adjsmonth,
        startYear: adjsyear,
        endMonth: adjemonth,
        endYear: adjeyear,
      };
    }

    if (orgID?.value) {
      clearErrors(['date_of_service_month', 'orgId']);
      methodApi('generateReport', {
        method: 'POST',
        restParams: { reportId: reportId },
        body: {
          payload,
          title: title,
          description: desc,
        },
        onSuccess: () => {
          setTimeout(() => router.push(`/reports/downloads`), 1500);
        },
        onError: () => {
          setSpinner(false);
        },
      });
    } else {
      if (orgID) {
        setError('orgId', {
          type: 'custom',
          message: 'this is required',
        });
      }
    }
  };

  const deleteReport = () => {
    methodApi('deleteReport', {
      restParams: {
        id: +saveReportId,
      },
      method: 'DELETE',
    }).then((response) => {
      showToast({
        status: 'success',
        title: 'Report deleted',
        description: 'Report Deleted Successfully',
      });
      router.push('/reports/favorites?add=true');
    });
  };

  const handleFavorites = () => {
    const {
      smonth,
      emonth,
      syear,
      eyear,
      adjsmonth,
      adjsyear,
      adjemonth,
      adjeyear,
    } = dateProcess();
    if (modalState.isEdit) {
      methodApi('postFavourites', {
        method: 'PUT',
        body: {
          title: modalState.title,
          description: modalState.desc,
          savereportId: +saveReportId,
        },
        onSuccess(res) {
          showToast({
            title: 'Report favorite updated',
            status: 'success',
          });
          setmodalState({ ...modalState, modal: false });
          router.push('/reports/favorites?add=true');
        },
      });
    } else {
      const configuration: any = {
        organization_number: +orgID.value,
        organization_name: orgID.label,
        filter: {
          label: dateOfService?.value,
          startMonth: parseInt(smonth as string),
          startYear: parseInt(syear as string),
          endMonth: parseInt(emonth as string),
          endYear: parseInt(eyear as string),
        },
        groups: groupNames?.length
          ? groupNames?.map((item: any) => item?.value)
          : null,
      };

      if (parentReportName == 'Lag Report') {
        configuration.filter.adjudicationRange = {
          startMonth: adjsmonth,
          startYear: adjsyear,
          endMonth: adjemonth,
          endYear: adjeyear,
        };
      }

      methodApi('postFavourites', {
        method: 'POST',
        body: {
          title: modalState.title || '',
          description: modalState.desc || '',
          reportId: +reportId,
          configuration,
        },
        onSuccess(res) {
          showToast({
            title: 'Report favorite updated',
            status: 'success',
          });
          setmodalState({ ...modalState, modal: false });
          router.push('/reports/favorites?add=true');
        },
      });
    }
  };

  const closeSchedule = () => {
    setScheduleOpen(false);
  };
  const handleSchedule = (type: string) => {
    setScheduleOpen(true);
  };

  const actionMenu = useMemo(
    () => (data: any) => {
      return [
        {
          clickHandler: () => {
            downloadSheetReport(orgID);
            return;
          },
          label: 'Generate Report',
          icon: <LuRefreshCcwDot fontSize={'18px'} />,
        },
        {
          clickHandler: () => {
            setmodalState({ ...modalState, modal: true });
            return;
          },
          label: 'Save As Favorite',
          icon: <IoMdStarOutline fontSize={'18px'} />,
        },
        {
          type: 'line',
        },
        {
          clickHandler: () => {
            // setSelectedData(data);
            setmodalState({
              ...modalState,
              modal: true,
              isEdit: true,
              title: title,
              desc: desc,
            });
            return;
          },
          label: 'Edit Favorite',
          icon: <FaEdit fontSize={'18px'} />,
        },
        {
          clickHandler: () => {
            handleSchedule('add');
            return;
          },
          label: 'Add Schedule',
          icon: <FaRegCalendarPlus fontSize={'18px'} />,
        },
        {
          clickHandler: () => {
            deleteReport();
          },
          label: 'Delete',
          icon: <RiDeleteBinLine fontSize={'18px'} />,
        },
      ];
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  //   Organization Handlers

  const onOrganizationChange = (value: any) => {
    reset({ groupNames: undefined });

    if (value) {
      reset();
      setValue('orgID', value);

      getApi('organizationGroups', {
        orgId: value?.value?.trim(),
      });
      getApi('reportingEligiblitySummaryFilters', {
        reportID: reportId as string,
        orgId: value?.value?.trim(),
      });
    } else {
      setValue('orgID', undefined);
    }
  };

  const onOrgInputChange = (value: string) => {
    getApi('organizations', {
      orgOffset: 0,
      orgName: value.trim(),
    });
  };

  const onOrgMenuScrollToBottom = () => {
    getApi('organizations', {
      orgOffset: organizations?.offset + 100,
      orgName: '',
    });
  };

  const flattenObject = (obj: any, parentKey = '', result?: any) => {
    for (const key in obj) {
      // eslint-disable-next-line no-prototype-builtins
      if (obj.hasOwnProperty(key)) {
        const newKey = parentKey ? `${parentKey}.${key}` : key;
        if (
          typeof obj[key] === 'object' &&
          obj[key] !== null &&
          !Array.isArray(obj[key])
        ) {
          flattenObject(obj[key], newKey, result);
        } else {
          result[newKey] = obj[key];
        }
      }
    }
    return result;
  };

  useEffect(() => {
    if (!getFavourites?.length) return;
    const reportFlatemList =
      getFavourites?.data.map((item: any) => flattenObject(item, '', {})) || [];

    const filteredReports = reportFlatemList.filter(
      (key: any) => key.saveReportId === saveReportById?.data[0]?.saveReportId
    );
    setScheduleData(filteredReports[0]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [getFavourites, saveReportById]);

  const getDateOfServiceOptions = () =>
    (reportingEligiblitySummaryFilters?.filters || []).map((e: any) => ({
      label: FilterNames[e.label as keyof typeof FilterNames],
      value: e.label,
    }));

  useEffect(() => {
    if (viewtype === 'preview' || viewtype === 'edit') {
      if (filteringList?.length > 0 && !watch('orgID')) {
        setValue(
          'orgID',
          filteringList.filter(
            (item: any) => +item?.value === +organizationId
          )[0]
        );
      }
      if (
        groupsOptions?.length > 0 &&
        typeof saveReportById?.data?.[0]?.definedFilters?.groups === 'object' &&
        saveReportById?.data?.[0].definedFilters?.groups?.length > 0 &&
        !groupNames &&
        +orgID?.value === +organizationId
      ) {
        setValue(
          'groupNames',
          groupsOptions.filter((item: any) =>
            saveReportById?.data?.[0].definedFilters.groups.includes(
              item?.value
            )
          )
        );
      }

      if (saveReportById?.data?.[0]?.definedFilters?.filter?.label) {
        if (
          saveReportById?.data?.[0]?.definedFilters?.filter?.adjudicationRange
        ) {
          setValue(
            'startMonthAdj',
            generateMonthOptions()?.filter(
              (item: any) =>
                parseInt(item?.value) ===
                parseInt(
                  saveReportById?.data?.[0].definedFilters?.filter
                    ?.adjudicationRange?.startMonth
                )
            )?.[0] || null
          );
          setValue('startYearAdj', {
            value:
              saveReportById?.data?.[0].definedFilters?.filter
                ?.adjudicationRange?.startYear,
            label:
              saveReportById?.data?.[0].definedFilters?.filter
                ?.adjudicationRange?.startYear,
          });
          setValue(
            'endMonthAdj',
            generateMonthOptions()?.filter(
              (item: any) =>
                parseInt(item?.value) ===
                parseInt(
                  saveReportById?.data?.[0].definedFilters?.filter
                    ?.adjudicationRange?.endMonth
                )
            )?.[0] || null
          );
          setValue('endYearAdj', {
            value:
              saveReportById?.data?.[0].definedFilters?.filter
                ?.adjudicationRange?.endYear,
            label:
              saveReportById?.data?.[0].definedFilters?.filter
                ?.adjudicationRange?.endYear,
          });
        }
        if (
          saveReportById?.data?.[0]?.definedFilters?.filter?.label ===
            'customDateRange' &&
          reportingEligiblitySummaryFilters?.filters?.length > 0
        ) {
          setValue('dateOfService', {
            value: 'customDateRange',
            label: 'Custom Date Range',
          });

          setValue(
            'startMonth',
            generateMonthOptions()?.filter(
              (item: any) =>
                parseInt(item?.value) ===
                parseInt(
                  saveReportById?.data?.[0].definedFilters?.filter?.startMonth
                )
            )?.[0] || null
          );
          setValue('startYear', {
            value: saveReportById?.data?.[0].definedFilters?.filter?.startYear,
            label: saveReportById?.data?.[0].definedFilters?.filter?.startYear,
          });
          setValue(
            'endMonth',
            generateMonthOptions()?.filter(
              (item: any) =>
                parseInt(item?.value) ===
                parseInt(
                  saveReportById?.data?.[0].definedFilters?.filter?.endMonth
                )
            )?.[0] || null
          );
          setValue('endYear', {
            label: saveReportById?.data?.[0].definedFilters?.filter?.endYear,
            value: saveReportById?.data?.[0].definedFilters?.filter?.endYear,
          });
        } else if (
          saveReportById?.data?.[0]?.definedFilters?.filter?.label ===
            'offCycle' &&
          reportingEligiblitySummaryFilters?.filters?.length > 0
        ) {
          setValue('dateOfService', {
            value: 'offCycle',
            label: 'Off Cycle',
          });
          setValue(
            'startMonth',
            generateMonthOptions()?.filter(
              (item: any) =>
                parseInt(item?.value) ===
                parseInt(
                  saveReportById?.data?.[0].definedFilters?.filter?.startMonth
                )
            )?.[0] || null
          );
          setValue('startYear', {
            value: saveReportById?.data?.[0].definedFilters?.filter?.startYear,
            label: saveReportById?.data?.[0].definedFilters?.filter?.startYear,
          });
          const enddate =
            String(
              saveReportById?.data?.[0].definedFilters?.filter?.endMonth
            )?.padStart(1, '0') +
            '/' +
            saveReportById?.data?.[0].definedFilters?.filter?.endYear;
          setValue('endDate', enddate);
        } else {
          setValue(
            'dateOfService',
            getDateOfServiceOptions().filter(
              (item: any) =>
                item?.value ===
                saveReportById?.data?.[0].definedFilters?.filter?.label
            )[0]
          );
          const startdate =
            String(
              saveReportById?.data?.[0].definedFilters?.filter?.startMonth
            )?.padStart(1, '0') +
            '/' +
            saveReportById?.data?.[0].definedFilters?.filter?.startYear;
          const enddate =
            String(
              saveReportById?.data?.[0].definedFilters?.filter?.endMonth
            )?.padStart(1, '0') +
            '/' +
            saveReportById?.data?.[0].definedFilters?.filter?.endYear;
          setValue('startDate', startdate);
          setValue('endDate', enddate);
        }
      } else return;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    filteringList,
    groupsOptions,
    saveReportById,
    reportingEligiblitySummaryFilters,
    viewtype,
  ]);

  const [intialCall, setIntialCall] = useState(true);

  useEffect(() => {
    if (
      viewtype === 'preview' &&
      filteringList?.length > 0 &&
      orgID &&
      saveReportById?.[0]?.definedFilters?.filter?.label &&
      reportingEligiblitySummaryFilters?.filters?.length > 0 &&
      groupsOptions?.length >= 0 &&
      intialCall
    ) {
      handleReport();
      setIntialCall(false);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    filteringList?.length,
    groupsOptions?.length,
    orgID,
    reportingEligiblitySummaryFilters?.filters?.length,
    saveReportById,
    viewtype,
  ]);

  const heading = useMemo(() => {
    if (viewtype === 'edit') return 'Modify Report Configuration';
    if (viewtype === 'preview') return 'Preview Report';
    return 'Report Configuration';
  }, [viewtype]);

  return (
    <Box w={'full'} fontFamily={'roboto'}>
      <ScheduleDrawerContent
        isScheduleOpen={isScheduleOpen}
        onCloseHandler={closeSchedule}
        addDrawer={scheduleData}
      />
      <ReportModal
        btnRef={btnRef}
        isOpen={modalState.modal}
        onClose={() =>
          setmodalState({ ...modalState, modal: false, isEdit: false })
        }
        handleFavourite={handleFavorites}
        header={modalState?.isEdit ? 'Edit Favorite' : 'Save As Favorite'}
        title={modalState.title}
        description={modalState.desc}
        setmodalState={setmodalState}
      />
      <Flex justifyContent={'space-between'} alignItems={'center'}>
        <Flex gap={2} onClick={() => router.back()} cursor={'pointer'}>
          <MdArrowBack
            size={'20px'}
            style={{
              backgroundColor: 'white',
              border: '1px solid grey',
            }}
          />
          <Box>
            <Text fontSize={'14px'} fontWeight={'400'} color={'#1A202C'}>
              Favorites
            </Text>
          </Box>
        </Flex>
        <ActionMenu
          // @ts-ignore
          data={actionMenu({}) as any}
          loading={false}
        />
      </Flex>
      <Box marginTop={'16px'}>
        <Text
          style={{
            fontSize: 24,
            fontWeight: '700',
          }}
        >
          {title}
        </Text>
        <Text
          style={{
            fontSize: 16,
            fontWeight: '400',
            color: '#303030',
            marginTop: 24,
            letterSpacing: 0.8,
          }}
        >
          {desc}
        </Text>
      </Box>
      <Box bg={'white'} boxShadow={'md'} py={2} px={4} marginTop={'24px'}>
        <Box>
          <Text
            style={{
              fontSize: 20,
              fontWeight: '700',
            }}
          >
            {heading}
          </Text>
          <Text>{parentReportName}</Text>
        </Box>

        <Flex flexDirection="column" gap={4} marginTop={'24px'}>
          <Flex gap={4}>
            <Box flex={1}>
              <Text mb={2} fontWeight={'bold'}>
                Organization
              </Text>
              <Controller
                name={'orgID'}
                control={control}
                render={({ field }) => (
                  <Select
                    {...field}
                    isClearable
                    placeholder={'Select Organization'}
                    options={filteringList}
                    chakraStyles={chakraStyles}
                    onInputChange={onOrgInputChange}
                    onMenuOpen={() => handleMenuOpen('menuIsOpen4', true)}
                    onChange={onOrganizationChange}
                    onMenuScrollToBottom={onOrgMenuScrollToBottom}
                    onMenuClose={() => handleMenuOpen('menuIsOpen4', false)}
                    menuIsOpen={menuOpenState.menuIsOpen4}
                    hideSelectedOptions={false}
                  />
                )}
              />
            </Box>
            {orgID &&
              groupsOptions.length >= 0 &&
              parentReportName !== 'Lag Report' && (
                <Box flex={1}>
                  <Text fontWeight={'bold'} mb={2}>
                    Group Name(s)
                  </Text>
                  <Controller
                    name={'groupNames'}
                    control={control}
                    render={({ field }) => (
                      <MultiSelect
                        {...field}
                        isClearable
                        placeholder="Select Group Names"
                        isMulti
                        options={groupsOptions}
                        value={field.value || null}
                        onChange={(value) => setValue('groupNames', value)}
                        hideSelectedOptions={false}
                      />
                    )}
                  />
                </Box>
              )}
          </Flex>

          {reportingEligiblitySummaryFilters?.filters?.length > 0 && orgID && (
            <DateFilter
              methods={method}
              reportingEligiblitySummaryFilters={
                reportingEligiblitySummaryFilters?.filters
              }
            />
          )}
          {parentReportName === 'Lag Report' && (
            <CustomDateFilter methods={method} />
          )}
        </Flex>
        <Box w={'full'} display={'flex'} justifyContent={'end'} mt={12} mb={2}>
          <Button
            variant={'solid'}
            onClick={() => {
              router.push(
                `/reports/singleFavourite?${searchParams?.toString()}&viewtype=reset`
              );
              control._reset({ orgID: null });

              reset({
                orgID: null,
                dateOfService: undefined,
                groupNames: undefined,
              });
              setCurrentData({});
            }}
            alignSelf={'end'}
            mr={4}
            colorScheme="gray"
          >
            Reset
          </Button>

          <Button
            colorScheme="blue"
            variant="outline"
            onClick={handleReport}
            alignSelf={'end'}
            mr={4}
            isDisabled={isButtonDisabledSingleReport(watch)}
            as={Flex}
            gap={3}
            alignItems={'center'}
            borderColor={'#0b5b8b'}
            color={'#0b5b8b'}
            cursor={'pointer'}
          >
            <FaRegEye color="inherit" />
            <Text color="inherit">Preview Data</Text>
          </Button>
          <Button
            colorScheme="blue"
            alignSelf={'end'}
            cursor={'pointer'}
            isDisabled={isButtonDisabledSingleReport(watch)}
            as={Flex}
            gap={3}
            alignItems={'center'}
            backgroundColor={'#0b5b8b'}
            onClick={handleConfiguration}
          >
            <Text color={'white'}>Save</Text>
          </Button>
        </Box>
      </Box>

      {spinner ? (
        <Center paddingY="48">
          <Spinner
            thickness="4px"
            speed="0.65s"
            emptyColor="gray.200"
            color="#03A262"
            size="xl"
          />
        </Center>
      ) : currentData?.reportData?.length > 0 ? (
        <Flex flexDir={'column'}>
          <Text fontSize={'24px'} my={'24px'}>
            Preview Data
          </Text>
          <SideScrollTable data={currentData?.reportData} />
        </Flex>
      ) : null}
      <ConfirmationModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        title="Subscriber Access"
        onConfirm={handleConfirm}
        cancelText="No"
        confirmText="Yes"
      >
        <Text mb={'24px'}>
          Changing the organization will reset list of subscribers back to
          default.
        </Text>
        <Text>Do you want to continue?</Text>
      </ConfirmationModal>
    </Box>
  );
};
export default SingleReportFavourite;
