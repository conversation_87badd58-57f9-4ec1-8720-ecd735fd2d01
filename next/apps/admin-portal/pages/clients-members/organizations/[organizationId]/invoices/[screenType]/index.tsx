import {
  Box,
  Checkbox,
  Flex,
  FormLabel,
  Heading,
  Text,
} from '@chakra-ui/react';
import {
  BillTo,
  EmailInTables,
  InvoiceButtonContainer,
  InvoicesClaimExport,
  InvoicesDistribution,
  InvoicesInput,
} from '@next/admin/components';
import {
  DEFAULT_INVOICES_VALUE,
  invoicesProps,
  invoiceTheme,
} from '@next/admin/constants';
import { useApi, useJavaApi } from '@next/shared/api';
import { onSubmitHandler, setInvoiceConfig } from '@next/shared/helpers';
import { useCustomToast, useQueryParams } from '@next/shared/hooks';
import { useParams } from 'next/navigation';
import { useRouter } from 'next/navigation';
import { useEffect, useMemo, useRef } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

const Index = () => {
  const params = useParams();
  const organizationId = params?.['organizationId'];
  const screenType = params?.['screenType'];
  const { configNo } = useQueryParams(['configNo']);
  const { organizationDetail } = useApi(['organizationDetail'], {
    orgId: organizationId as string | number,
  });

  const showToast = useCustomToast();
  const router = useRouter();

  const {
    methodApi,
    getApi,
    getInvoiceConfigDetail,
    targetFtpList,
    eligibilityConfigEncryptionkey,
  } = useJavaApi(['eligibilityConfigEncryptionkey', 'targetFtpList']);

  const methods = useForm<invoicesProps>({
    defaultValues: {
      financialName: organizationDetail?.name,
      organizationNo: organizationId as string,
      ...DEFAULT_INVOICES_VALUE,
    },
  });

  const targetList = useMemo(
    () =>
      targetFtpList?.map((item: any) => ({
        label: item.name,
        value: item.ftpSiteNo,
      })) || [],
    [targetFtpList]
  );

  useEffect(() => {
    if (!configNo || screenType === 'create') return;
    getApi('getInvoiceConfigDetail', {
      invoiceConfigNo: configNo,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [configNo]);

  useEffect(() => {
    if (getInvoiceConfigDetail) {
      setInvoiceConfig(getInvoiceConfigDetail, methods.setValue);

      screenType === 'copy' && methods.setValue('name', '');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [getInvoiceConfigDetail, targetFtpList, eligibilityConfigEncryptionkey]);

  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    screenType === 'create' &&
      methods.setValue('financialName', organizationDetail?.name);
    methods.setValue('organizationName', organizationDetail?.name);
  }, [methods, organizationDetail, screenType]);

  const onSubmit = (data: invoicesProps) => {
    if (!methods.formState.isDirty && screenType === 'edit') {
      router.push(
        `/clients-members/organizations/${methods.watch(
          'organizationNo'
        )}/dashboard`
      );
      return;
    }

    onSubmitHandler(
      data,
      methodApi,
      screenType as string,
      methods,
      configNo,
      getInvoiceConfigDetail,
      showToast,
      router
    );
  };

  const submitHandler = () => inputRef.current?.click();

  const addHandler = (email: string | undefined) => {
    const emailPattern = /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,4}$/;
    if (!email) return;

    email = email.toLowerCase();
    if (!emailPattern.test(email)) {
      showToast({
        title: 'Enter Correct Email',
        status: 'error',
      });
      return;
    }
    const value =
      methods.watch('availabilityDistributionList')?.split(';') || [];
    value.push(email);

    methods.setValue('availabilityDistributionList', value.join(';'), {
      shouldDirty: true,
    });
  };

  const getEmailsValue = () =>
    methods
      .watch('availabilityDistributionList')
      ?.split(';')
      ?.map((item) => ({ email: item })) || [];

  const removeHandler = (email: string) => {
    const value = methods
      .watch('availabilityDistributionList')
      ?.split(';')
      .filter((item) => item !== email);
    methods.setValue(
      'availabilityDistributionList',
      value?.length === 0 ? null : value?.join(';'),
      { shouldDirty: true }
    );
  };

  return (
    <Box>
      <Heading
        size="sm"
        ml={4}
        color={'#06096a'}
        mt={8}
        textTransform={'capitalize'}
      >
        {screenType} Invoice Configuration
      </Heading>
      <Box
        style={{
          border: '1px dotted black',
          padding: 13,
          marginTop: 16,
          backgroundColor: '#e6e7e8',
        }}
      >
        <FormProvider {...methods}>
          <form onSubmit={methods.handleSubmit(onSubmit)}>
            <Flex>
              <FormLabel style={invoiceTheme.invoicesLabel}>
                Organization:
              </FormLabel>
              <Text style={{ fontSize: 12, fontWeight: 600 }}>
                {organizationDetail?.name}
              </Text>
            </Flex>
            <Flex alignItems={'center'} gap={3}>
              <InvoicesInput label="Name*" name="name" />
              <Heading fontStyle={'italic'} fontSize={12} fontFamily={'serif'}>
                NOTE: Please omit the Organization Name as it will be
                automatically appended to the entered value.
              </Heading>
            </Flex>
            <InvoicesInput label="Financial Name" name="financialName" />
            <Flex pl={25} mt={2} ml={143}>
              <Checkbox
                isChecked={methods.watch('emailNotification')}
                onChange={(e) =>
                  methods.setValue('emailNotification', e.target.checked, {
                    shouldDirty: true,
                  })
                }
              >
                <Text
                  style={{
                    fontSize: 12,
                    color: '#06096a',
                    marginBottom: 0,
                    whiteSpace: 'nowrap',
                  }}
                >
                  Provide Email notification of the availability and
                  distribution of Invoice documents?
                </Text>
              </Checkbox>
            </Flex>
            {methods.watch('distribution') === '3' && (
              <Flex mt={3}>
                <BillTo />
              </Flex>
            )}
            {methods.watch('emailNotification') && (
              <Box
                style={{
                  width: 400,
                  marginLeft: 167,
                }}
                marginY={5}
              >
                <EmailInTables
                  data={getEmailsValue()}
                  addHandler={addHandler}
                  removeHandler={removeHandler}
                />
              </Box>
            )}
            <Flex gap={10} mt={3}>
              <InvoicesDistribution
                targetList={targetList}
                eligibilityConfigEncryptionkey={eligibilityConfigEncryptionkey}
              />
              {['0', '1', '2'].includes(methods.watch('distribution')) && (
                <BillTo />
              )}
            </Flex>
            <InvoicesClaimExport />
            <InvoiceButtonContainer submitHandler={submitHandler} />
            <input type="submit" style={{ display: 'none' }} ref={inputRef} />
          </form>
        </FormProvider>
      </Box>
    </Box>
  );
};

export default Index;
