// @ts-check

const { composePlugins, withNx } = require('@nx/next');

/**
 * @type {import('@nx/next/plugins/with-nx').WithNxOptions}
 */
const nextConfig = {
  nx: {
    svgr: false, // Set this to true if you want to use SVGR for importing SVGs
  },
  env: {
    // Add any environment variables you need here
    AUTH0_NAMESPACE_QA_KEY: process.env.AUTH0_NAMESPACE_QA_KEY || '',
    AUTH0_NAMESPACE_DEMO_KEY: process.env.AUTH0_NAMESPACE_DEMO_KEY || '',
    AUTH0_NAMESPACE_STAGE_KEY: process.env.AUTH0_NAMESPACE_STAGE_KEY || '',
    AUTH0_NAMESPACE_PROD_KEY: process.env.AUTH0_NAMESPACE_PROD_KEY || '',
    DATADOG_APPLICATION_ID: process.env.AP_DATADOG_APPLICATION_ID || '',
    DATADOG_CLIENT_TOKEN: process.env.AP_DATADOG_CLIENT_TOKEN || '',
    DATADOG_SAMPLE_RATE: process.env.DATADOG_SAMPLE_RATE || '100',
    NEXT_PUBLIC_BEN_ADMIN_URL: process.env.NEXT_PUBLIC_BEN_ADMIN_URL || '',
    NEXT_PUBLIC_PROTECT_URL: process.env.NEXT_PUBLIC_PROTECT_URL || '',
    SHOW_BEN_ADMIN_V2: process.env.SHOW_BEN_ADMIN_V2 || '',

    DATADOG_APPLICATION_VERSION:
      process.env.DATADOG_APPLICATION_VERSION || '1.0.0',
    SERVICE_URL:
      process.env.NODE_ENV === 'development'
        ? 'https://admin-portal-service-qa.rxbenefits.cloud'
        : 'http://admin-portal-service:8080',
  },
  typescript: {
    ignoreBuildErrors: true, // Disable TypeScript build errors during build
  },
  /**
   * Extend the webpack configuration to alias @emotion/react so that only one
   * instance of it is bundled. This prevents warnings and potential styling issues
   * caused by multiple copies of @emotion/react being loaded.
   *
   * @param {import('webpack').Configuration} config - Current webpack config
   * @returns {import('webpack').Configuration} - Modified webpack config
   */
  webpack: (config) => {
    // Ensure config.resolve and config.resolve.alias exist
    config.resolve = config.resolve || {};
    config.resolve.alias = config.resolve.alias || {};

    // If alias is an object, we can assign @emotion/react to a single resolved path
    if (!Array.isArray(config.resolve.alias)) {
      // Alias ensures all imports of @emotion/react map to this single path
      config.resolve.alias['@emotion/react'] =
        require.resolve('@emotion/react');
    } else {
      // If it's an array, we can't assign a key-value pair to it
      console.warn(
        'config.resolve.alias is an array; cannot assign @emotion/react alias.'
      );
    }

    return config;
  },
};

module.exports = composePlugins(withNx)(nextConfig);
