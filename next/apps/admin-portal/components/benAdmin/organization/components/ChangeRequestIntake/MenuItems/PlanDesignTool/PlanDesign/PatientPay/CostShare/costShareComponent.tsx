import { CostShareDesign } from 'apps/admin-portal/components/benAdmin';
import { useIsESIProduct } from 'apps/admin-portal/components/benAdmin/organization/hooks/useIsESIHook';
import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import DynamicCollectionSection from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/DynamicCollectionSection';
import React, { memo, useCallback, useEffect, useMemo, useRef } from 'react';
import { useWatch } from 'react-hook-form';

import { CostShareForm } from './components/costShareForm';
// Import components
import { CostShareModalController } from './components/costShareModal/CostShareModalController';
import { createTableComponent } from './components/CostShareTableComponent';
import { COST_SHARE_TYPES, MESSAGES } from './constants';
import { useCostShareForm } from './hooks/useCostShareForm';
// Import hooks
import { useCostShareHelpers } from './hooks/useCostShareHelpers';
import { useCostShareSync } from './hooks/useCostShareSync';
import { useCostShareTable } from './hooks/useCostShareTable';
import { useUrlParamSync } from './hooks/useUrlParamSync';
// Import types and constants
import { CostShareComponentProps, FilterConfig } from './types';

/**
 * CostShareComponent handles the management of cost share tiers for plan designs
 * It supports both standard and unbreakable cost share types and manages synchronization between them
 */
const CostShareComponent: React.FC<CostShareComponentProps> = ({
  formMethods,
  onUpdateActiveItem,
  backIndicator,
  continueIndicator,
  isUnbreakable = false,
  title = 'Cost Share Design',
  description = 'Select the cost share tiers that apply to this plan.',
  modalTitle = 'Select Cost Share Tiers',
  basePath,
  sourceLabel = 'Use the same information as standard',
}) => {
  const isUpdatingFormRef = useRef(false);
  const submitHandler = useSaveChangeRequestHandler(formMethods);

  // Check if this is an ESI product
  const isESIProduct = useIsESIProduct(formMethods);

  // Initialize helpers
  const helpers = useCostShareHelpers(
    formMethods,
    basePath || '',
    isUnbreakable
  );
  const {
    formatTitle,
    getFormPath,
    createFilterItems,
    createNewItem,
    updateFormState,
    findMatchingItems,
    getCurrentTabFilter,
  } = helpers;

  const {
    costShareTierMap,
    costShareTierDaysSupplyMap,
    pharmacyChannelMap,
    costShareTypeMap,
    esiDrugListMap,
    networkStatusMap,
  } = usePicklistMaps();

  // Define path
  const path = useMemo(() => getFormPath(basePath), [getFormPath, basePath]);

  // Create filter for this component
  const filterItems = useCallback(
    (item: CostShareDesign) => createFilterItems(isUnbreakable)(item),
    [createFilterItems, isUnbreakable]
  );

  // Watch form data
  const costShareItems = useWatch({
    control: formMethods.control,
    name: path,
    defaultValue: [],
  }) as CostShareDesign[];

  // Get filtered items
  const standardItems = useMemo(
    () => costShareItems?.filter(filterItems),
    [costShareItems, filterItems]
  );

  // Setup sync hooks
  const { syncStandardToUnbreakable, removeAllUnbreakableMirrors } =
    useCostShareSync({
      costShareItems,
      formMethods,
      path,
      isUnbreakable,
      isUpdatingFormRef,
      standardItems,
    });

  const { isSyncModeEnabled } = useUrlParamSync({
    formMethods,
    isUnbreakable,
    syncStandardToUnbreakable,
    removeAllUnbreakableMirrors,
  });

  // Setup form hooks
  const {
    modalKey,
    setModalKey,
    selectedIndexes,
    isCostShareModalOpen,
    closeCostShareModal,
    prepareTierList,
    handleModalSave,
    handleCustomDelete,
    handleGetStarted: baseHandleGetStarted,
    handleOpenModal,
    handleAddChannel,
    handleEditRow,
  } = useCostShareForm(
    formMethods,
    path,
    isUnbreakable,
    costShareItems,
    filterItems,
    createNewItem,
    isSyncModeEnabled
  );

  // Setup table hooks
  const { tableDataMap, showOptions } = useCostShareTable(
    costShareItems,
    handleEditRow
  );

  // Optimize form watch subscription
  useEffect(() => {
    const subscription = formMethods.watch(
      (_: any, { name }: { name?: string }) => {
        if (name?.startsWith(path)) {
          setModalKey((prev) => prev + 1);
        }
      }
    );
    return () => subscription.unsubscribe();
  }, [formMethods, path, setModalKey]);

  // Handle wrapper functions
  const handleUpdateFormState = useCallback(
    (items: CostShareDesign[], emptyState = false) => {
      updateFormState(items, emptyState, path, setModalKey);
    },
    [updateFormState, path, setModalKey]
  );

  const handleRemoveUnbreakableItems = useCallback(() => {
    handleUpdateFormState(standardItems);
  }, [standardItems, handleUpdateFormState]);

  const handleSaveAndExit = useCallback(() => {
    submitHandler(formMethods.getValues());
  }, [submitHandler, formMethods]);

  const handleBack = useCallback(() => {
    if (!backIndicator) return;
    if (onUpdateActiveItem) {
      onUpdateActiveItem(backIndicator);
    }
  }, [backIndicator, onUpdateActiveItem]);

  const handleContinue = useCallback(() => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(continueIndicator);
    }
  }, [continueIndicator, onUpdateActiveItem]);

  const handleMirrorChange = useCallback(
    (checked: boolean) => {
      if (checked) {
        syncStandardToUnbreakable();
        handleUpdateFormState(formMethods.getValues(path) || [], true);
      } else {
        handleRemoveUnbreakableItems();
      }
    },
    [
      syncStandardToUnbreakable,
      handleRemoveUnbreakableItems,
      formMethods,
      path,
      handleUpdateFormState,
    ]
  );

  const handleGetStarted = useCallback(
    (item: CostShareDesign) => {
      baseHandleGetStarted(item, findMatchingItems);
    },
    [baseHandleGetStarted, findMatchingItems]
  );

  // Create table component
  const tableComponent = useMemo(
    () =>
      createTableComponent(tableDataMap, getCurrentTabFilter, handleEditRow),
    [tableDataMap, getCurrentTabFilter, handleEditRow]
  );

  // Create filter config
  const filterConfig: FilterConfig = useMemo(
    () => ({
      isDistinct: true,
      indicator: 'tier_ind',
      filter: filterItems,
    }),
    [filterItems]
  );

  // Prepare tier list for form
  const preparedTierList = useMemo(
    () => prepareTierList(costShareTierMap),
    [prepareTierList, costShareTierMap]
  );

  // Item identifier for dynamic collection
  const itemIdentifier: [string, any] = useMemo(
    () => [
      '  pre_packaged_ind',
      isUnbreakable ? COST_SHARE_TYPES.UNBREAKABLE : COST_SHARE_TYPES.STANDARD,
    ],
    [isUnbreakable]
  );

  // Format titles
  const finalTitle = useMemo(() => formatTitle(title), [formatTitle, title]);
  const finalModalTitle = useMemo(
    () => formatTitle(modalTitle),
    [formatTitle, modalTitle]
  );

  return (
    <>
      <DynamicCollectionSection
        key={`${modalKey}-${isUnbreakable}`}
        basePath={path}
        formMethods={{
          ...formMethods,
          getValues: () => ({
            ...formMethods.getValues(),
            [path]: costShareItems,
          }),
        }}
        generalForm={
          <CostShareForm
            costShareTiers={preparedTierList}
            onSave={handleModalSave}
            unbreakable={isUnbreakable}
          />
        }
        title={finalTitle}
        description={description}
        emptyMessage={MESSAGES.EMPTY_MESSAGE(isUnbreakable)}
        skipMessage={MESSAGES.SKIP_MESSAGE(isUnbreakable)}
        createNewButtonText={MESSAGES.CREATE_NEW_BUTTON}
        isOptional={true}
        modalTitle={finalModalTitle}
        onBack={backIndicator ? handleBack : undefined}
        onContinue={handleContinue}
        onSaveExit={handleSaveAndExit}
        customHandleSaveItem={handleModalSave}
        itemLabelKey="tier_ind"
        useAccordion={true}
        onOpenModal={handleOpenModal}
        filterItems={filterItems}
        itemIdentifier={itemIdentifier}
        onGetStarted={handleGetStarted}
        showOptionsButton={showOptions}
        tableComponent={tableComponent}
        distinct={filterConfig}
        onAddChannel={handleAddChannel}
        addChannelButtonText={MESSAGES.ADD_CHANNEL_BUTTON}
        onRemoveItem={handleCustomDelete}
        emptyStateOptions={
          isUnbreakable
            ? {
                showMirrorCheckbox: true,
                mirrorLabel: sourceLabel,
                onUncheck: handleRemoveUnbreakableItems,
                onCheck: handleMirrorChange,
              }
            : undefined
        }
      />

      <CostShareModalController
        isOpen={isCostShareModalOpen}
        onClose={closeCostShareModal}
        formMethods={formMethods}
        basePath={path}
        selectedIndexes={selectedIndexes}
        daySupply={costShareTierDaysSupplyMap}
        pharmacyChannel={pharmacyChannelMap}
        networkStatus={networkStatusMap}
        costShareType={costShareTypeMap}
        drugList={isESIProduct ? esiDrugListMap : undefined}
      />
    </>
  );
};

export default memo(CostShareComponent);
