import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import {
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { SubCategoryType } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { UseFormReturn } from 'react-hook-form';
import { z } from 'zod';

import { transitionConfig } from '../../../../../Tabs/PlanDesign/MemberExperience/Config/transitionConfig';

const percentValidation = z
  .string()
  .regex(/^(\d{1,3})(\.\d{1,2})?$/, {
    message: 'Must be a valid percentage with up to 2 decimal places',
  })
  .refine((value) => parseFloat(value) <= 100, {
    message: 'Percentage cannot exceed 100',
  })
  .transform((val) => parseFloat(val));

export const useTransitionFilesForm = (
  currentDetails: Partial<OrganizationDetails>,
  formMethods: UseFormReturn<OrganizationDetails>
) => {
  const { accumTransferMap, yesNoMap } = usePicklistMaps();

  const plan_transition = currentDetails?.plan?.plan_transition;
  const currentPBM = currentDetails?.plan?.product?.vendor?.legal_entity_id;

  const planId = currentDetails?.plan?.plan_id;
  if (!plan_transition && planId) {
    formMethods?.setValue('plan.plan_transition.plan_id', planId);
  }
  const subCategories: SubCategoryType[] = [
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          '6 Months Claims Files',
          'dropdownSelect',
          transitionConfig.historical_claims_ind,
          currentDetails?.plan?.plan_transition?.historical_claims_ind,
          {
            isRequired: true,
            optionsMap: yesNoMap,
            placeholder: 'Select Yes/No',
            infoText: 'Please select your 6 Months Claims Files preference',
          }
        ),
        defineFormField(
          'ORT Transition File',
          'dropdownSelect',
          transitionConfig.ort_transition_file_ind,
          currentDetails?.plan?.plan_transition?.ort_transition_file_ind,
          {
            isRequired: true,
            optionsMap: yesNoMap,
            placeholder: 'Select ORT Transition File',
            infoText: 'Please select your ORT Transition File preference',
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Mail Order Percentage',
          'input',
          transitionConfig.mail_order_percentage,
          currentDetails?.plan?.plan_transition?.mail_order_percentage,
          {
            placeholder: 'Enter Mail Order Percentage',
            infoText: 'Please enter your Mail Order Percentage',
            validations: percentValidation.optional(),
          }
        ),
        defineFormField(
          'PA File',
          'dropdownSelect',
          transitionConfig.pa_file_ind,
          currentDetails?.plan?.plan_transition?.pa_file_ind,
          {
            isRequired: true,
            optionsMap: yesNoMap,
            placeholder: 'Select PA File',
            infoText: 'Please select your PA File',
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Outbound Claims Files',
          'input',
          transitionConfig.outbound_claims_file,
          currentDetails?.plan?.plan_transition?.outbound_claims_file,
          {
            placeholder: 'Enter Outbound Claims Files',
            infoText: 'Please enter your Outbound Claims Files',
            validations: z
              .string()
              .max(255, 'Value cannot exceed 255 characters'),
          }
        ),
        defineFormField(
          'Carrier to Carrier Process',
          'dropdownSelect',
          transitionConfig.carrier_to_carrier_ind,
          currentDetails?.plan?.plan_transition?.carrier_to_carrier_ind,
          {
            isRequired: true,
            optionsMap: yesNoMap,
            placeholder: 'Select Yes/No',
            infoText:
              'Please select your Carrier to Carrier Process preference',
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Patient Transfer Process',
          'dropdownSelect',
          transitionConfig.patient_transfer_ind,
          currentDetails?.plan?.plan_transition?.patient_transfer_ind,
          {
            isRequired: true,
            optionsMap: yesNoMap,
            placeholder: 'Select Yes/No',
            infoText: 'Please select your Patient Transfer Process preference',
          }
        ),
        defineFormField(
          'Transition Notes',
          'input',
          transitionConfig.notes,
          currentDetails?.plan?.plan_transition?.notes,
          {
            placeholder: 'Enter your notes',
            infoText: 'Please enter your Transition Notes',
            validations: z
              .string()
              .max(2000, 'Value cannot exceed 2000 characters'),
          }
        ),
      ]),
    ]),
  ];
  if (currentPBM === 2)
    // Express Scripts
    subCategories.push(
      defineSubCategory('ESI Only', '', [
        defineInlineFieldGroup([
          defineFormField(
            'Accum Transfer',
            'dropdownSelect',
            transitionConfig.accum_transfer_ind,
            currentDetails?.plan?.plan_transition?.accum_transfer_ind,
            {
              isRequired: true,
              optionsMap: accumTransferMap,
              placeholder: 'Select Accum Transfer',
              infoText: 'Please select your Accum Transfer Process preference',
            }
          ),
        ]),
      ])
    );
  else if (currentPBM === 1)
    // Caremark
    subCategories.push(
      defineSubCategory('CMK', '', [
        defineInlineFieldGroup([
          defineFormField(
            'Follow Me Logic Process',
            'dropdownSelect',
            transitionConfig.follow_me_ind,
            currentDetails?.plan?.plan_transition?.follow_me_ind,
            {
              isRequired: true,
              optionsMap: yesNoMap,
              placeholder: 'Select Yes/No',
              infoText: 'Please select your Follow me logic preference',
            }
          ),
        ]),
      ])
    );
  return { subCategories };
};
