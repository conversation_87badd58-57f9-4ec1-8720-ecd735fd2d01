import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import {
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { SubCategoryType } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { z } from 'zod';

import { welcomeLettersConfig } from '../../../../../Tabs/PlanDesign/MemberExperience/Config/welcomeLettersConfig';

export const useWelcomeKitForm = (
  currentDetails: Partial<OrganizationDetails>
) => {
  const { memberPacketsMap, yesNoMap } = usePicklistMaps();
  const currentPBM = currentDetails?.plan?.product?.vendor?.legal_entity_id;

  const subCategories: SubCategoryType[] = [
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Name on Member Materials',
          'input',
          welcomeLettersConfig.member_materials_name,
          currentDetails?.plan?.plan_material?.member_materials_name,
          {
            isRequired: true,
            placeholder: 'Enter Name on Member Materials',
            infoText: 'Please enter your name on Member Materials',
            validations: z
              .string()
              .max(100, 'Name cannot be more than 100 characters'),
          }
        ),
        defineFormField(
          'Member Packets',
          'dropdownSelect',
          welcomeLettersConfig.member_packets_ind,
          currentDetails?.plan?.plan_material?.member_packets_ind,
          {
            isRequired: true,
            optionsMap: memberPacketsMap,
            placeholder: 'Select Member Packets',
            infoText: 'Please select your Member Packets preference',
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Disruption Letters',
          'dropdownSelect',
          welcomeLettersConfig.disruption_letters_ind,
          currentDetails?.plan?.plan_material?.disruption_letters_ind,
          {
            isRequired: true,
            optionsMap: yesNoMap,
            placeholder: 'Select Yes/No',
            infoText: 'Please select your Disruption Letters preference',
          }
        ),
        defineFormField(
          'Specialty Letters',
          'dropdownSelect',
          welcomeLettersConfig.specialty_letters_ind,
          currentDetails?.plan?.plan_material?.specialty_letters_ind,
          {
            isRequired: true,
            optionsMap: yesNoMap,
            placeholder: 'Select Yes/No',
            infoText: 'Please select your Specialty Letters preference',
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Mail Order Letters',
          'dropdownSelect',
          welcomeLettersConfig.mail_order_letters_ind,
          currentDetails?.plan?.plan_material?.mail_order_letters_ind,
          {
            isRequired: true,
            optionsMap: yesNoMap,
            placeholder: 'Select Yes/No',
            infoText: 'Please select your Mail Order Letters preference',
          }
        ),
        defineFormField(
          'Allow for customization',
          'dropdownSelect',
          welcomeLettersConfig.allow_customization_ind,
          currentDetails?.plan?.plan_material?.allow_customization_ind,
          {
            isRequired: true,
            optionsMap: yesNoMap,
            placeholder: 'Select Yes/No',
            infoText: 'Please select your Customization preference',
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'ID Cards & Member Materials Note',
          'input',
          welcomeLettersConfig.notes,
          currentDetails?.plan?.plan_material?.notes,
          {
            placeholder: 'Enter your notes',
            infoText: 'Please enter your Member Materials Notes',
            validations: z
              .string()
              .max(2000, 'Value cannot exceed 2000 characters'),
          }
        ),
      ]),
    ]),
  ];
  if (currentPBM === 9)
    //Optum
    subCategories.push(
      defineSubCategory('Optum', '', [
        defineInlineFieldGroup([
          defineFormField(
            'Marketing Outreach Opt Out Home Delivery',
            'dropdownSelect',
            welcomeLettersConfig.marketing_outreach_home_delivery_ind,
            currentDetails?.plan?.plan_material
              ?.marketing_outreach_home_delivery_ind,
            {
              isRequired: true,
              optionsMap: yesNoMap,
              placeholder: 'Select Yes/No',
              infoText:
                'Please select your Marketing Outreach Opt Out Home Delivery preference',
            }
          ),
          defineFormField(
            'Marketing Outreach Opt Out Specialty Service',
            'dropdownSelect',
            welcomeLettersConfig.marketing_outreach_specialty_service_ind,
            currentDetails?.plan?.plan_material
              ?.marketing_outreach_specialty_service_ind,
            {
              isRequired: true,
              optionsMap: yesNoMap,
              placeholder: 'Select Yes/No',
              infoText:
                'Please select your Marketing Outreach Opt Out Specialty Service preference',
            }
          ),
        ]),
      ])
    );

  return { subCategories };
};
