import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { clientConfig } from 'apps/admin-portal/components/benAdmin/organization/components/Tabs/PlanDesign/ClientProfile/Config/clientConfig';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import {
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { SubCategoryType } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { z } from 'zod';

/**
 * useClientInformationForm Hook
 *
 * Generates form subcategories (sections) based on the provided organization details.
 * Uses dot notation for field names so that the final form JSON is nested.
 *
 * @param currentDetails - Partial organization data used to prefill form fields.
 * @returns An object containing the generated subCategories.
 */
export function useClientInformationForm(
  currentDetails: Partial<OrganizationDetails>
) {
  const {
    erisaMap,
    benefitPeriodsMap,
    terminationReasonMap,
    monthsMap,
    planClassMap,
  } = usePicklistMaps();

  // Build the subcategories using the helper functions.
  const subCategories: SubCategoryType[] = [
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Legal Name',
          'input',
          clientConfig.legal_entity_name,
          currentDetails?.organization?.legal_entity_name,
          {
            infoText: 'Please enter name of Organization',
            placeholder: 'Enter legal name',
            isRequired: true,
            validations: z
              .string({ required_error: 'Legal Name is required.' })
              .min(5, 'Organization name must be at least 5 characters')
              .max(100, 'Organization name must be less than 100 characters'),
          }
        ),
        defineFormField(
          'Employer Address',
          'input',
          clientConfig.address_line_1,
          currentDetails?.organization?.address?.address_line_1,
          {
            placeholder: 'Enter employer address',
            isRequired: true,
            infoText: 'Employer Address',
            validations: z
              .string({ required_error: 'Employer Address is required.' })
              .min(1, 'Employer Address cannot be empty.')
              .max(255, 'Address must be less than 255 characters'),
          }
        ),
      ]),
    ]),
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Benefit Period',
          'dropdownSelect',
          clientConfig.benefit_period_ind,
          currentDetails?.plan?.plan_designs?.[0]?.plan_design_details?.[0]
            ?.benefit_period_ind,
          {
            optionsMap: benefitPeriodsMap,
            isRequired: true,
            infoText: 'Choose one option',
          }
        ),
        defineFormField(
          'Plan Year/Renewal',
          'dropdownSelect',
          clientConfig.plan_year_renewal,
          currentDetails?.plan?.plan_year_renewal,
          {
            optionsMap: benefitPeriodsMap,
            infoText: 'Choose one option',
            isRequired: true,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Renewal Month',
          'dropdownSelect',
          clientConfig.renewal_month,
          currentDetails?.plan?.renewal_month,
          {
            optionsMap: monthsMap,
            infoText: 'Choose one option',
            isRequired: true,
          }
        ),
        defineFormField(
          'Annual Reporting Start Month',
          'dropdownSelect',
          clientConfig.annual_reporting_start_month,
          currentDetails?.plan?.annual_reporting_start_month,
          {
            optionsMap: monthsMap,
            allowSearch: true,
            placeholder: 'Enter annual reporting start month',
            infoText: 'Select from the dropdown options',
            isRequired: true,
          }
        ),
      ]),
    ]),
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Plan Class',
          'dropdownSelect',
          clientConfig.product_class_id,
          currentDetails?.plan?.product?.product_class_id,
          {
            optionsMap: planClassMap,
            isRequired: true,
            infoText:
              'Please select if the plan is self-funded or fully-insured',
            placeholder: 'Enter plan class',
          }
        ),
        defineFormField(
          'ERISA Status',
          'dropdownSelect',
          clientConfig.erisa_ind,
          currentDetails?.plan?.erisa_ind,
          {
            optionsMap: erisaMap,
            isRequired: true,
            infoText: 'Please select if the plan is ERISA or Non-ERISA',
            placeholder: 'Select an Option',
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Previous PBM',
          'input',
          clientConfig.previous_pbm,
          currentDetails?.plan?.previous_pbm,
          {
            infoText: 'Select enter plans previous PBM',
            placeholder: 'Enter previous PBM',
            validations: z
              .string()
              .max(100, 'Previous PBM cannot exceed 100 characters')
              .optional(),
          }
        ),
      ]),
    ]),
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Primary Termination Reason',
          'dropdownSelect',
          clientConfig.primary_termination_reason,
          currentDetails?.organization?.primary_termination_reason,
          {
            infoText: 'Please explain the main reason for termination',
            placeholder: 'Primary Reason of Termination',
            optionsMap: terminationReasonMap,
          }
        ),
        defineFormField(
          'Secondary Termination Reason',
          'dropdownSelect',
          clientConfig.secondary_termination_reason,
          currentDetails?.organization?.secondary_termination_reason,
          {
            infoText: 'Please explain the secondary reason for termination',
            placeholder: 'Secondary Reason of Termination',
            optionsMap: terminationReasonMap,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Termination Details',
          'input',
          clientConfig.termination_notes,
          currentDetails?.organization?.termination_notes,
          {
            isRequired: true,
            infoText: 'Please list the details for termination',
            placeholder: 'Details of Termination',
            validations: z
              .string()
              .max(32768, 'Termination Details cannot exceed 32768 characters'),
          }
        ),
      ]),
    ]),
  ];

  return {
    subCategories,
  };
}
