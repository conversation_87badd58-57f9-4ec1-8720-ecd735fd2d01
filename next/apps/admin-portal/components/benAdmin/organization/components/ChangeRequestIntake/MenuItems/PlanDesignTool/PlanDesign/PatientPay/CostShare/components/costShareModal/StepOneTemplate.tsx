import {
  Box,
  Checkbox,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Input,
  Select,
  Text,
  useTheme,
  VStack,
} from '@chakra-ui/react';
import { DateControl } from 'apps/admin-portal/components/benAdmin';
import React, { useCallback, useEffect } from 'react';
import { useFormContext } from 'react-hook-form';
import { z } from 'zod';

import { createTierNameSchema, customPharmacyNameSchema } from './validation';

export interface StepOneTemplateProps {
  pharmacyChannel: string;
  onPharmacyChannelChange: (value: string) => void;
  patientPayType: string;
  onPatientPayTypeChange: (value: string) => void;
  networkStatus: string;
  onNetworkStatusChange: (value: string) => void;
  customPharmacyName: string;
  onCustomPharmacyNameChange: (value: string) => void;
  documentGeneration: string[];
  onDocumentGenerationChange: (values: string[]) => void;
  tierName: string;
  onTierNameChange: (value: string) => void;
  tierNameError: string | null;
  onTierNameErrorChange: (error: string | null) => void;
  effectiveDate: Date | undefined;
  onEffectiveDateChange: (date: Date | undefined) => void;
  expirationDate: Date | undefined;
  onExpirationDateChange: (date: Date | undefined) => void;
  description?: string;
  pharmacyChannelOptions?: Record<string, string>;
  networkStatusOptions?: Record<string, string>;
  costShareTypeOptions?: Record<string, string>;
  drugList?: Record<string, string>;
  drugListInd: string | null;
  onDrugListChange: (value: string) => void;
  existingTierNames: string[];
  currentId?: number | null;
  originalName?: string;
  dateStyles?: any;
}

/**
 * Template for Step 1 of the cost share modal
 * Contains pharmacy channel, patient pay type, and document generation inputs
 */
export const StepOneTemplate: React.FC<StepOneTemplateProps> = ({
  pharmacyChannel,
  onPharmacyChannelChange,
  patientPayType,
  onPatientPayTypeChange,
  networkStatus,
  onNetworkStatusChange,
  customPharmacyName,
  onCustomPharmacyNameChange,
  documentGeneration,
  onDocumentGenerationChange,
  tierName,
  onTierNameChange,
  tierNameError,
  onTierNameErrorChange,
  effectiveDate,
  onEffectiveDateChange,
  expirationDate,
  onExpirationDateChange,
  dateStyles = {},
  description = "Here's a brief description placeholder of what this page will have the user work on and make changes to. It should be able to provide context and instruction to the user to confidently answer information.",
  pharmacyChannelOptions,
  networkStatusOptions,
  costShareTypeOptions,
  drugList,
  drugListInd,
  onDrugListChange,
  existingTierNames,
  currentId,
  originalName,
}) => {
  const isCustomPharmacy = pharmacyChannel === '5';
  const theme = useTheme();
  const formMethods = useFormContext();
  const {
    formState: { errors },
  } = formMethods;

  const formStyles = {
    field: {
      _focus: {
        borderColor: 'green.500',
        boxShadow: `0 0 0 1px ${theme.colors.green[500]}`,
      },
    },
  };

  const [isDirty, setIsDirty] = React.useState(false);

  const validateTierName = useCallback(
    (value: string) => {
      if (!isDirty) return true;

      try {
        // Pass the original name to the validation schema
        const schema = createTierNameSchema(
          existingTierNames,
          currentId,
          originalName
        );
        schema.parse(value);
        return true;
      } catch (error) {
        if (error instanceof z.ZodError) {
          return error.errors[0].message;
        }
        return 'Invalid tier name';
      }
    },
    [existingTierNames, currentId, isDirty, originalName]
  );

  // Validate custom pharmacy name
  const validateCustomPharmacyName = (value: string) => {
    if (!isCustomPharmacy) return true;
    try {
      customPharmacyNameSchema.parse(value);
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        return error.errors[0].message;
      }
      return 'Invalid custom pharmacy name';
    }
  };

  // Update validation state whenever tier name changes
  useEffect(() => {
    if (!isDirty) return;

    const validationResult = validateTierName(tierName);

    // Also update the error message
    if (typeof validationResult === 'string') {
      onTierNameErrorChange(validationResult);
    } else {
      onTierNameErrorChange(null);
    }
  }, [tierName, validateTierName, onTierNameErrorChange, isDirty]);

  // Create option maps for dropdowns
  const pharmacyChannelOptionsMap = React.useMemo(() => {
    return pharmacyChannelOptions || {};
  }, [pharmacyChannelOptions]);

  const networkStatusOptionsMap = React.useMemo(() => {
    return networkStatusOptions || {};
  }, [networkStatusOptions]);

  const costShareTypeOptionsMap = React.useMemo(() => {
    return costShareTypeOptions || {};
  }, [costShareTypeOptions]);

  const drugListOptionsMap = React.useMemo(() => {
    return drugList || {};
  }, [drugList]);

  // Determine if we should show Drug List dropdown
  const showDrugList = !!drugList && Object.keys(drugList).length > 0;

  return (
    <Box width="100%">
      <VStack spacing={4} align="stretch">
        <Text fontSize="sm" color="gray.600" mb={2}>
          {description}
        </Text>

        <FormControl isRequired isInvalid={!!tierNameError && isDirty}>
          <FormLabel>Tier Name</FormLabel>
          <Input
            value={tierName}
            onChange={(e) => {
              setIsDirty(true);
              const error = validateTierName(e.target.value);
              onTierNameChange(e.target.value);

              // Update the error message based on validation result
              if (typeof error === 'string') {
                onTierNameErrorChange(error);
              } else {
                onTierNameErrorChange(null);
              }
            }}
            onBlur={() => setIsDirty(true)}
            placeholder="Enter tier name"
            sx={formStyles.field}
          />
          {tierNameError && isDirty && (
            <FormErrorMessage>{tierNameError}</FormErrorMessage>
          )}
        </FormControl>

        <Flex gap={4}>
          <Box flex="1">
            <FormControl isRequired>
              <FormLabel>Pharmacy Channel</FormLabel>
              <Select
                value={pharmacyChannel}
                onChange={(e) => {
                  onPharmacyChannelChange(e.target.value);
                }}
                placeholder="Select pharmacy channel"
                size="sm"
                sx={formStyles.field}
              >
                {Object.entries(pharmacyChannelOptionsMap).map(
                  ([value, label]) => (
                    <option key={value} value={value}>
                      {label}
                    </option>
                  )
                )}
              </Select>
            </FormControl>
          </Box>
          {isCustomPharmacy && (
            <FormControl isRequired isInvalid={!!errors.customPharmacyName}>
              <FormLabel>Custom Pharmacy</FormLabel>
              <Input
                value={customPharmacyName}
                onChange={(e) => {
                  const error = validateCustomPharmacyName(e.target.value);
                  onCustomPharmacyNameChange(e.target.value);
                  if (typeof error === 'string') {
                    // Handle error
                  }
                }}
                placeholder="Enter custom pharmacy name"
                sx={formStyles.field}
                flex="1"
              />
              {errors.customPharmacyName && (
                <FormErrorMessage>
                  {String(errors.customPharmacyName.message)}
                </FormErrorMessage>
              )}
            </FormControl>
          )}
        </Flex>

        <FormControl isRequired>
          <FormLabel>Patient Pay Type</FormLabel>
          <Select
            value={patientPayType}
            onChange={(e) => {
              onPatientPayTypeChange(e.target.value);
            }}
            placeholder="Select patient pay type"
            size="sm"
            sx={formStyles.field}
          >
            {Object.entries(costShareTypeOptionsMap).map(([value, label]) => (
              <option key={value} value={value}>
                {label}
              </option>
            ))}
          </Select>
        </FormControl>

        <FormControl isRequired>
          <FormLabel>Network Status</FormLabel>
          <Select
            value={networkStatus}
            onChange={(e) => onNetworkStatusChange(e.target.value)}
            placeholder="Select network status"
            size="sm"
            sx={formStyles.field}
          >
            {Object.entries(networkStatusOptionsMap).map(([value, label]) => (
              <option key={value} value={value}>
                {label}
              </option>
            ))}
          </Select>
        </FormControl>

        <Flex gap={4} width="100%">
          <FormControl isRequired flex="1">
            <DateControl
              label="Effective Date"
              value={effectiveDate}
              onChange={onEffectiveDateChange}
              dateProps={{
                propsConfigs: {
                  ...dateStyles,
                  inputProps: { size: 'sm', borderRadius: 4 },
                },
              }}
              placeholder="Select effective date"
              showClearButton={!!effectiveDate}
              clearAction={() => onEffectiveDateChange(undefined)}
            />
          </FormControl>

          <FormControl flex="1">
            <DateControl
              label="Expiration Date"
              value={expirationDate}
              onChange={onExpirationDateChange}
              dateProps={{
                propsConfigs: {
                  ...dateStyles,
                  inputProps: { size: 'sm', borderRadius: 4 },
                },
              }}
              placeholder="Select expiration date"
              showClearButton={!!expirationDate}
              clearAction={() => onExpirationDateChange(undefined)}
            />
          </FormControl>
        </Flex>
        <Flex gap={4} width="100%">
          <FormControl flex="1" mt="30px">
            <Checkbox
              isChecked={documentGeneration.includes('Include in PBC')}
              onChange={(e) =>
                onDocumentGenerationChange(
                  e.target.checked ? ['Include in PBC'] : []
                )
              }
              colorScheme="green"
            >
              Include in PBC
            </Checkbox>
          </FormControl>
          {/* Drug List Dropdown - Only shown if drugList is available */}
          {showDrugList && (
            <FormControl flex="1">
              <FormLabel>Drug List</FormLabel>
              <Select
                value={drugListInd ?? ''}
                onChange={(e) => {
                  onDrugListChange(e.target.value);
                }}
                placeholder="Select drug list"
                size="sm"
                sx={formStyles.field}
              >
                {Object.entries(drugListOptionsMap).map(([value, label]) => (
                  <option key={value} value={value}>
                    {label}
                  </option>
                ))}
              </Select>
            </FormControl>
          )}
        </Flex>
      </VStack>
    </Box>
  );
};

export default StepOneTemplate;
