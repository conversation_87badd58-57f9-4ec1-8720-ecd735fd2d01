import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import {
  formatDate,
  setEffectiveStatus,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Table/utils/tableUtils';
import { TemplateFieldConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';

// Define field configuration
export const getGlobalAccountInformationField = (
  orgDetails: Partial<OrganizationDetails>
): Partial<TemplateFieldConfig>[] => [
  {
    label: 'Account Effective Date',
    value: formatDate(orgDetails?.organization?.effective_date),
    name: 'organization.effective_date',
  },
  {
    label: 'Account ID',
    value: orgDetails?.plan?.datanet_id,
    name: 'plan.datanet_id',
  },
  {
    label: 'Current PBM',
    value: orgDetails?.plan?.product?.vendor?.name,
    name: 'plan.product.vendor.chosen_pbm',
  },
  {
    label: 'Plan Effective Date',
    value: formatDate(orgDetails?.plan?.effective_date),
    name: 'plan.effective_date',
  },
  {
    label: 'Brokerage Firm',
    value: orgDetails?.organization?.brokerage_firm,
    name: 'organization.brokerage_firm',
  },
  {
    label: 'Paid Contract No.',
    value: orgDetails?.plan?.paid_contract_number,
    name: 'plan.paid_contract_number',
  },
  {
    label: 'Covered Employees',
    value: orgDetails?.organization?.covered_employees,
    name: 'organization.covered_employees',
  },
  {
    label: 'Line of Business',
    value: orgDetails?.organization?.line_of_business,
    name: 'organization.line_of_business',
  },
  {
    label: 'Group Umbrella No.',
    value: orgDetails?.plan?.group_umbrella_number,
    name: 'plan.group_umbrella_number',
  },
  {
    label: 'Covered Members',
    value: orgDetails?.organization?.covered_members,
    name: 'organization.covered_members',
  },
  {
    label: 'Industry',
    value: orgDetails?.organization?.industry,
    name: 'organization.industry',
  },
  {
    label: 'Carrier Number',
    value: orgDetails?.plan?.carrier_number,
    name: 'plan.carrier_number',
  },
  {
    label: 'Account Type',
    value: orgDetails?.plan?.product?.name,
    name: 'product.account_type',
  },
  {
    label: 'Industry Vertical',
    value: orgDetails?.organization?.industry_vertical,
    name: 'organization.industry_vertical',
  },
  {
    label: 'Address',
    value: orgDetails?.organization?.address?.address_line_1,
    name: 'organization.address.address_line_1',
  },
  {
    label: 'Eligibility Vendor',
    value: orgDetails?.organization?.eligibility_vendor_name,
    name: 'organization.eligibility_vendor_name',
  },
  {
    label: 'Effective Status',
    value: setEffectiveStatus(
      orgDetails?.organization?.expiration_date,
      orgDetails?.organization?.active_ind
    ),
    name: 'organization.effective_status',
  },
  {
    label: 'Expiration Date',
    value: formatDate(orgDetails?.organization?.expiration_date),
    name: 'organization.expiration_date',
  },
];
