import { UseFormReturn } from 'react-hook-form';

import { OrganizationDetails } from '../../../Models/interfaces';
import { getGlobalAccountInformationField } from './FieldData/globalAccountInfoFieldData';

// import { mockData } from './globalMock';

export function useGlobalAccountInformationFieldGroup(
  orgDetails: OrganizationDetails,
  formMethods: UseFormReturn<any>
) {
  const { handleSubmit, reset, formState, watch, register } = formMethods;
  const { errors } = formState;

  return {
    subtitle: 'Global Account Information',
    fields: getGlobalAccountInformationField(watch()),
    columns: 3,
    handleSubmit,
    reset,
    errors,
    formMethods,
    register,
    onCancel: () => reset(orgDetails),
  };
}
