/**
 * getClientInformationFields
 *
 * This function defines the field configuration for the client information section,
 * which is used to build a single UI section via a reusable template.
 *
 * Each field configuration includes:
 *  - label: The display label for the field.
 *  - value: The value retrieved from the backend. For nested properties, optional chaining is used (e.g., `orgDetails?.address?.address_line_1`).
 *  - name: The JSON key path used to structure the final form data. It must match the value's path but without the optional chaining (e.g., `address.address_line_1`).
 *
 * IMPORTANT:
 *  The 'name' property must exactly mirror the backend value structure (minus the question marks)
 *  to ensure that the final JSON output is correctly structured. For example, if a field's value is
 *  defined as `orgDetails?.address?.address_line_1`, then its corresponding name must be `address.address_line_1`.
 *
 * Developer Enforcement Ideas:
 *  - Create a helper function or utility that generates the 'name' property automatically from the expected data path.
 *  - Write unit tests or custom ESLint rules to verify that for each field configuration, the 'name'
 *    follows the correct pattern based on the backend value structure.
 *
 * This consistency is crucial because any mismatch will result in an incorrect JSON structure in the final form submission.
 */

// Example usage of the flattened client configuration

import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistInterface';
import { TemplateFieldConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';

import { clientConfig } from '../Config/clientConfig';

// Define field configuration with the flattened client config
export const getClientInformationFields = (
  orgDetails: Partial<OrganizationDetails>,
  orgName: string,
  maps: Partial<PicklistMaps>
): Partial<TemplateFieldConfig>[] => [
  {
    label: 'Legal Name',
    value: orgName,
    name: 'orgName',
  },
  {
    label: 'Employer Address',
    name: clientConfig.address_line_1,
    value: orgDetails?.organization?.address?.address_line_1,
  },
  {
    label: 'Benefit Period',
    value:
      orgDetails?.plan?.plan_designs?.[0]?.plan_design_details?.[0]
        ?.benefit_period_ind,
    name: clientConfig.benefit_period_ind,
    type: 'dropdownSelect',
    optionsMap: maps.benefitPeriodsMap,
  },
  {
    label: 'Plan Year/Renewal',
    value: orgDetails?.plan?.plan_year_renewal,
    name: clientConfig.plan_year_renewal,
    type: 'dropdownSelect',
    optionsMap: maps.benefitPeriodsMap,
  },
  {
    label: 'Renewal Month',
    value: orgDetails?.plan?.renewal_month,
    name: clientConfig.renewal_month,
    type: 'dropdownSelect',
    optionsMap: maps.monthsMap,
  },
  {
    label: 'Annual Reporting Start Month',
    value: orgDetails?.plan?.annual_reporting_start_month,
    name: clientConfig.annual_reporting_start_month,
    type: 'dropdownSelect',
    optionsMap: maps.monthsMap,
  },
  {
    label: 'Plan Class',
    value: orgDetails?.plan?.product?.product_class_id,
    name: clientConfig.product_class_id,
    type: 'dropdownSelect',
    optionsMap: maps.planClassMap,
  },
  {
    label: 'ERISA Status',
    value: orgDetails?.plan?.erisa_ind,
    name: clientConfig.erisa_ind,
    type: 'dropdownSelect',
    optionsMap: maps.erisaMap,
  },
  {
    label: 'Previous PBM',
    value: orgDetails?.plan?.previous_pbm,
    name: clientConfig.previous_pbm,
  },
];
