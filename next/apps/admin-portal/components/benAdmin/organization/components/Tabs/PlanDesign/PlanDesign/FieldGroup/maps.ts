export const planYearRenewalMap = {
  1: 'January through December (Calendar Year)',
  2: 'February through January',
  3: 'March through February',
  4: 'April through March',
  5: 'May through April',
  6: 'June through May',
  7: 'July through June',
  8: 'August through July',
  9: 'September through August',
  10: 'October through September',
  11: 'November through October',
  12: 'December through November',
};

export const yesNoMap = {
  0: 'No',
  1: 'Yes',
};
export const pharmacyChannelsMap: Record<number, string> = {
  1: 'Mail',
  2: 'Retail',
  3: 'Specialty',
  4: 'In-House Pharmacies',
  5: 'Custom',
};
export const compoundsMap = {
  0: 'Tiered',
  1: 'Brand',
  2: 'Most Expensive Ingredient',
  3: 'Preferred Brand Copay',
};

export const dispenseASWrittenMap = {
  0: 'Brand Copay + Difference',
  1: '100% of Brand Cost',
  2: 'Generic Copay + Difference',
  3: 'DAW 2 - Brand Copay + Difference',
  4: 'None',
  5: 'DAW 2 Generic Copay + Difference',
  6: 'Other',
  7: 'View on Benefit (ESI ONLY)',
};

export const cdhCodeMap = {
  1: 'INFERTILIT',
  2: 'SMOKINGCES',
  3: 'CHEMOSPLTY',
  4: 'CHEMOTHERA',
  5: 'DIABETES',
  6: 'MENTALHLTH',
  7: 'SPECIALTY',
};

export const accumTierMap = {
  1: 'Deductible',
  2: 'Maximum Out of Pocket',
  3: 'Drug List Specific Cap',
  4: 'Lifetime Cap',
  5: 'Benefit Cap',
};

export const excludeDrugListMap = {
  '100088 - Fertility': '100088 - Fertility',
  '100097 - Insulin (Std)': '100097 - Insulin (Std)',
  '100125 - Antihemophilia Agents': '100125 - Antihemophilia Agents',
  '100127 - Smoking Deterrents': '100127 - Smoking Deterrents',
  '100128 - Smoking Cessation Medications':
    '100128 - Smoking Cessation Medications',
  '100133 - Anti-Obesity Standard': '100133 - Anti-Obesity Standard',
  '100257 - Diabetic Supplies/Insulin Needles, Syringes/Insulin (Std)':
    '100257 - Diabetic Supplies/Insulin Needles, Syringes/Insulin (Std)',
  '100258 - Diabetic Supplies/Insulin Needles, Syringes':
    '100258 - Diabetic Supplies/Insulin Needles, Syringes',
  '100276 - Minimed insulin pumps, batteries, Sil-Serter, reservoir, OH-Tron Plus Pumps, OD-Tron pumps':
    '100276 - Minimed insulin pumps, batteries, Sil-Serter, reservoir, OH-Tron Plus Pumps, OD-Tron pumps',
  '100400 - Inhaler Assisting Devices': '100400 - Inhaler Assisting Devices',
  '100403 - Lancets': '100403 - Lancets',
  '100404 - Blood Glucose Monitors w/glucowatch':
    '100404 - Blood Glucose Monitors w/glucowatch',
  '100429 - Glucowatch products': '100429 - Glucowatch products',
  '100466 - Needles': '100466 - Needles',
  '100497 - Injectable Specialty': '100497 - Injectable Specialty',
  '100498 - Peak Flow Meters': '100498 - Peak Flow Meters',
  '100499 - Immune Globulins': '100499 - Immune Globulins',
  '100508 - Prilosec OTC': '100508 - Prilosec OTC',
  '100617 - Bronchodilators': '100617 - Bronchodilators',
  '100619 - Xanthines STD': '100619 - Xanthines STD',
  '100632 - Std Claritin/Claritin-D': '100632 - Std Claritin/Claritin-D',
  '100637 - Lancet Devices (Std)': '100637 - Lancet Devices (Std)',
  '100668 - Hypoglycemics Rx and OTC': '100668 - Hypoglycemics Rx and OTC',
  '100685 - Custom Drug List TracPac': '100685 - Custom Drug List TracPac',
  '100698 - Chronic Conditions': '100698 - Chronic Conditions',
  '100787 - PPI (STD, OTC)': '100787 - PPI (STD, OTC)',
  '100956 - Non-Injectable Specialty': '100956 - Non-Injectable Specialty',
  '101847 - Fluticasone/ Salmeterol/Advair':
    '101847 - Fluticasone/ Salmeterol/Advair',
  '101876 - Antineoplastics oral anticancer agents':
    '101876 - Antineoplastics oral anticancer agents',
  '103905 - Nebulizer Equipment': '103905 - Nebulizer Equipment',
  '105810 - Blood Sugar Test Strips': '105810 - Blood Sugar Test Strips',
  '105980 - Sub Q Insulin Pumps (STD)': '105980 - Sub Q Insulin Pumps (STD)',
  '113626 - Cholesterol': '113626 - Cholesterol',
  '113761 - Aspirin': '113761 - Aspirin',
  '117342 - Only Pump Supplies, No Pumps':
    '117342 - Only Pump Supplies, No Pumps',
  '127369 - Legend Generic PPI’s (does not include OTC’s)':
    '127369 - Legend Generic PPI’s (does not include OTC’s)',
  '130154 - Fertility': '130154 - Fertility',
  '132282 - Fertility Regulators': '132282 - Fertility Regulators',
  '135007 - GROWTH PROMOTING AGENTS': '135007 - GROWTH PROMOTING AGENTS',
  '135008 - ED Meds': '135008 - ED Meds',
  '136100 - Diabetic Supplies': '136100 - Diabetic Supplies',
  '136847 - Hyper and Hypoglycemics': '136847 - Hyper and Hypoglycemics',
  '137432 - Lancets and Devices': '137432 - Lancets and Devices',
  '143981 - INJ<>INSULIN (DOSE14/50): (DF 14/50)':
    '143981 - INJ<>INSULIN (DOSE14/50): (DF 14/50)',
  '150907 - Smoking Cess, OTC': '150907 - Smoking Cess, OTC',
  '153685 - Static EBD00077: EBD: VBID ASTHMA-COPD DRUGS':
    '153685 - Static EBD00077: EBD: VBID ASTHMA-COPD DRUGS',
  '161282 - ACA Merged Offering Vitamin D and Calcium/Vitamin D':
    '161282 - ACA Merged Offering Vitamin D and Calcium/Vitamin D',
  '162641 - Nitrates': '162641 - Nitrates',
  '188601 - Bowel Prep Agents - Generics and SSB':
    '188601 - Bowel Prep Agents - Generics and SSB',
  '217827 - Std All Inclusive': '217827 - Std All Inclusive',
  '225585 - Diabetic Medications': '225585 - Diabetic Medications',
  '231291 - Standard Preventive Meds': '231291 - Standard Preventive Meds',
  '272929 - Standard GN Preventive Meds':
    '272929 - Standard GN Preventive Meds',
  '303862 - Standard plus Preventive Meds':
    '303862 - Standard plus Preventive Meds',
  '315711 - Specialty': '315711 - Specialty',
  '318233 - Unysis Custom Drug List': '318233 - Unysis Custom Drug List',
  '339109 - Standard plus GN Preventive Meds':
    '339109 - Standard plus GN Preventive Meds',
  '339878 - Nexium OTC': '339878 - Nexium OTC',
  '359071 - Springpoint Senior Living, Inc.':
    '359071 - Springpoint Senior Living, Inc.',
  '380014 - South State Bank': '380014 - South State Bank',
  '380046 - Female HDSS (Female Sexual Disfunction)':
    '380046 - Female HDSS (Female Sexual Disfunction)',
  '410332 - Diabetic Meds and Supplies (Generic and Brand)':
    '410332 - Diabetic Meds and Supplies (Generic and Brand)',
  '411198 - Reslizumab': '411198 - Reslizumab',
  '432501 - Hypertensives/Blood Pressure':
    '432501 - Hypertensives/Blood Pressure',
  '485657 - Northeastern Supply, Inc Custom DL':
    '485657 - Northeastern Supply, Inc Custom DL',
  '492390 - Custom DL (Specialty & Injectables & Non Injectable)':
    '492390 - Custom DL (Specialty & Injectables & Non Injectable)',
  '492409 - Custom DL (Specialty & Injectables & Non Injectable)':
    '492409 - Custom DL (Specialty & Injectables & Non Injectable)',
  '497641 - SaveOnSP DL': '497641 - SaveOnSP DL',
  '498866 - RxB LCG': '498866 - RxB LCG',
  '517922 - Parr Custom PM Drug List': '517922 - Parr Custom PM Drug List',
  '525380 - Amarillo College of Hairdressing, Inc.':
    '525380 - Amarillo College of Hairdressing, Inc.',
  '525499 - Patient Assurace Program (PAP) Insulin only':
    '525499 - Patient Assurace Program (PAP) Insulin only',
  '532875 - CDH Patient Assurace Program (PAP) plus Prev Meds minus PAP Insulin':
    '532875 - CDH Patient Assurace Program (PAP) plus Prev Meds minus PAP Insulin',
  '536489 - Std Prev Meds minus Patient Assurace Program (PAP) Insulin':
    '536489 - Std Prev Meds minus Patient Assurace Program (PAP) Insulin',
  '541261 - ESSE Health bypass DED list':
    '541261 - ESSE Health bypass DED list',
  '542011 - Diabetic Supplies - Omnipod (ONLY)':
    '542011 - Diabetic Supplies - Omnipod (ONLY)',
  '542295 - Custom Preventative Drug List':
    '542295 - Custom Preventative Drug List',
  '544383 - Hubspot CDH list': '544383 - Hubspot CDH list',
  '545563 - Gunnison Valley Health CDH': '545563 - Gunnison Valley Health CDH',
  '551549 - Trilogy CDH bypass': '551549 - Trilogy CDH bypass',
  '565223 - Tx Baptist Custom': '565223 - Tx Baptist Custom',
  '594835 - PAP Drug List for NPF': '594835 - PAP Drug List for NPF',
  '596496 - PAP list Basic': '596496 - PAP list Basic',
  '597428 - DME (OmniPod Dash)': '597428 - DME (OmniPod Dash)',
  '607957 - Generic Antidiabetic Meds': '607957 - Generic Antidiabetic Meds',
  '613473 - NonACA PM/Oral Anticancer agents':
    '613473 - NonACA PM/Oral Anticancer agents',
  '614273 (STD HDHP G, gen PPI, All Ins)':
    '614273 (STD HDHP G, gen PPI, All Ins)',
  '616110 - Covid Oral Antiviral': '616110 - Covid Oral Antiviral',
  '641225 - DL RSV STD DL': '641225 - DL RSV STD DL',
  '641684 - Wright Brothers - Child of DL 595435':
    '641684 - Wright Brothers - Child of DL 595435',
  '130154 - FERTILITY AGENTS (ALL) LUPRON 1 AND PROGESTERONE OIL':
    '130154 - FERTILITY AGENTS (ALL) LUPRON 1 AND PROGESTERONE OIL',
  'No Exclude Drug List': 'No Exclude Drug List',
};

export const drugListMap = {
  '100088 - Fertility Agents (Std, All)':
    '100088 - Fertility Agents (Std, All)',
  '100097 - Insulin (Std)': '100097 - Insulin (Std)',
  '100125 - Antihemophilia Agents': '100125 - Antihemophilia Agents',
  '100127 - Smoking Deterrents': '100127 - Smoking Deterrents',
  '100128 - Smoking Cessation Medications':
    '100128 - Smoking Cessation Medications',
  '100129 - Smoking Deterrents': '100129 - Smoking Deterrents',
  '100133 - Anti-Obesity Standard': '100133 - Anti-Obesity Standard',
  '100257 - Diabetic Supplies/Insulin Needles, Syringes/Insulin (Std)':
    '100257 - Diabetic Supplies/Insulin Needles, Syringes/Insulin (Std)',
  '100276 - Minimed insulin pumps, batteries, Sil-Serter, reservoir, OH-Tron Plus Pumps, OD-Tron pumps':
    '100276 - Minimed insulin pumps, batteries, Sil-Serter, reservoir, OH-Tron Plus Pumps, OD-Tron pumps',
  '100362 - Self Administered Injectibles':
    '100362 - Self Administered Injectibles',
  '100400 - Inhaler Assisting Devices': '100400 - Inhaler Assisting Devices',
  '100404 - Blood Glucose Monitors w/glucowatch':
    '100404 - Blood Glucose Monitors w/glucowatch',
  '100429 - Glucowatch products': '100429 - Glucowatch products',
  '100466 - Needles': '100466 - Needles',
  '100497 - Injectable Specialty': '100497 - Injectable Specialty',
  '100498 - Peak Flow Meters': '100498 - Peak Flow Meters',
  '100499 - Immune Globulins': '100499 - Immune Globulins',
  '100508 - Prilosec OTC': '100508 - Prilosec OTC',
  '100617 - Bronchodilators': '100617 - Bronchodilators',
  '100619 - Xanthines STD': '100619 - Xanthines STD',
  '100632 - Std Claritin/Claritin-D': '100632 - Std Claritin/Claritin-D',
  '100668 - Hypoglycemics Rx and OTC': '100668 - Hypoglycemics Rx and OTC',
  '100685 - Custom Drug List TracPac': '100685 - Custom Drug List TracPac',
  '100698 - Chronic Conditions': '100698 - Chronic Conditions',
  '100956 - Non-Injectable Specialty': '100956 - Non-Injectable Specialty',
  '101847 - Fluticasone/ Salmeterol/Advair':
    '101847 - Fluticasone/ Salmeterol/Advair',
  '103905 - Nebulizer Equipment': '103905 - Nebulizer Equipment',
  '105810 - Blood Sugar Test Strips': '105810 - Blood Sugar Test Strips',
  '105980 - Sub Q Insulin Pumps (STD)': '105980 - Sub Q Insulin Pumps (STD)',
  '113626 - Cholesterol': '113626 - Cholesterol',
  '113761 - Aspirin': '113761 - Aspirin',
  '117342 - Only Pump Supplies, No Pumps':
    '117342 - Only Pump Supplies, No Pumps',
  '127369 - Legend Generic PPI’s (does not include OTC’s)':
    '127369 - Legend Generic PPI’s (does not include OTC’s)',
  '130154 - FERTILITY AGENTS (ALL) LUPRON 1 AND PROGESTERONE OIL':
    '130154 - FERTILITY AGENTS (ALL) LUPRON 1 AND PROGESTERONE OIL',
  '132282 - Fertility Regulators': '132282 - Fertility Regulators',
  '135007 - GROWTH PROMOTING AGENTS': '135007 - GROWTH PROMOTING AGENTS',
  '135008 - ED Meds': '135008 - ED Meds',
  '136100 - Diabetic Supplies': '136100 - Diabetic Supplies',
  '136847 - Hyper and Hypoglycemics': '136847 - Hyper and Hypoglycemics',
  '143981 - INJ<>INSULIN (DOSE14/50): (DF 14/50)':
    '143981 - INJ<>INSULIN (DOSE14/50): (DF 14/50)',
  '150907 - Smoking Cess, OTC': '150907 - Smoking Cess, OTC',
  '153685 - Static EBD00077: EBD: VBID ASTHMA-COPD DRUGS':
    '153685 - Static EBD00077: EBD: VBID ASTHMA-COPD DRUGS',
  '161282 - ACA Merged Offering Vitamin D and Calcium/Vitamin D':
    '161282 - ACA Merged Offering Vitamin D and Calcium/Vitamin D',
  '162641 - Nitrates': '162641 - Nitrates',
  '188601 - Bowel Prep Agents - Generics and SSB':
    '188601 - Bowel Prep Agents - Generics and SSB',
  '217827 - Std All Inclusive': '217827 - Std All Inclusive',
  '225585 - Diabetic Medications': '225585 - Diabetic Medications',
  '231291 - Standard Preventive Meds': '231291 - Standard Preventive Meds',
  '272929 - Standard GN Preventive Meds':
    '272929 - Standard GN Preventive Meds',
  '303862 - Standard plus Preventive Meds':
    '303862 - Standard plus Preventive Meds',
  '315711 - Specialty DL': '315711 - Specialty DL',
  '318233 - Unysis Custom Drug List': '318233 - Unysis Custom Drug List',
  '339109 - Standard plus GN Preventive Meds':
    '339109 - Standard plus GN Preventive Meds',
  '339878 - Nexium OTC': '339878 - Nexium OTC',
  '359071 - Springpoint Senior Living, Inc.':
    '359071 - Springpoint Senior Living, Inc.',
  '380046 - Female HDSS (Female Sexual Disfunction)':
    '380046 - Female HDSS (Female Sexual Disfunction)',
  '410332 - Diabetic Meds and Supplies (Generic and Brand)':
    '410332 - Diabetic Meds and Supplies (Generic and Brand)',
  '411136 - Cardiovascular/Cardiac Drugs':
    '411136 - Cardiovascular/Cardiac Drugs',
  '411198 - Reslizumab': '411198 - Reslizumab',
  '432501 - Hypertensives/Blood Pressure':
    '432501 - Hypertensives/Blood Pressure',
  '485657 - Northeastern Supply, Inc Custom DL':
    '485657 - Northeastern Supply, Inc Custom DL',
  '492390 - Custom DL (Specialty & Injectables & Non Injectable)':
    '492390 - Custom DL (Specialty & Injectables & Non Injectable)',
  '492409 - Custom DL (Specialty & Injectables & Non Injectable)':
    '492409 - Custom DL (Specialty & Injectables & Non Injectable)',
  '497641 - SaveOnSP DL': '497641 - SaveOnSP DL',
  '498866 - RxB LCG': '498866 - RxB LCG',
  '542011 - Diabetic Supplies - Omnipod (ONLY)':
    '542011 - Diabetic Supplies - Omnipod (ONLY)',
  '542295 - Custom Preventative Drug List':
    '542295 - Custom Preventative Drug List',
  '565223 - Tx Baptist Custom': '565223 - Tx Baptist Custom',
  '594835 - PAP Drug List for NPF': '594835 - PAP Drug List for NPF',
  '596496 - PAP list Basic': '596496 - PAP list Basic',
  '597428 - DME (OmniPod Dash)': '597428 - DME (OmniPod Dash)',
  '607957 - Generic Antidiabetic Meds': '607957 - Generic Antidiabetic Meds',
  '613473 - NonACA PM/Oral Anticancer agents':
    '613473 - NonACA PM/Oral Anticancer agents',
  '614273 (STD HDHP G, gen PPI, All Ins)':
    '614273 (STD HDHP G, gen PPI, All Ins)',
  '616110 - Covid Oral Antiviral': '616110 - Covid Oral Antiviral',
  '641225 - DL RSV STD DL': '641225 - DL RSV STD DL',
  '641684 - Wright Brothers - Child of DL 595435':
    '641684 - Wright Brothers - Child of DL 595435',
  'No Include Drug List': 'No Include Drug List',
};

export const pharmacyChannelMap = {
  1: 'Retail/Mail Combo (Standard)',
  2: 'Mail Only',
  3: 'Retail only (Card & Paper claims)',
};

export const networkStatusMap = {
  1: 'In and Out of Network (Standard)',
  2: 'INN - In Network Only',
  3: 'OON - Out of Network Only',
  4: 'In-house/Preferred Pharmacy',
};

export const formularyMap = {
  1: 'Formulary and Non Formulary (Standard)',
  2: 'Formulary Drugs only',
  3: 'Non Formulary Drugs only',
};

export const drugTypeMap = {
  1: 'All (Standard)',
  2: 'Brand only',
  3: 'Generic only',
};

export const sharedMap = {
  1: 'Accumulator is Not Shared',
  2: 'Accumulator Is Shared (Bidirectional)',
  3: 'Accumulator is Shared (NextGen)',
  4: 'Accumulator is Shared (NextGen 2)',
};

export const penaltyMap = {
  0: 'No',
  1: 'Yes',
  2: 'View on Benefit *ESI ONLY*',
};

export const accumPeriodMap = {
  1: 'Calendar Year (Jan-Dec)',
  2: 'Plan Year',
};

export const benefitPeriodMap = {
  1: '12 Months (Standard)',
  2: 'Lifetime',
  3: 'Other',
};

export const carryOverMap = {
  0: 'No',
  1: 'Yes',
  2: 'Fourth Quarter',
};

export const hraMemberMap = {
  1: 'Direct Claim',
  2: 'Debit Card',
  3: 'Real-Time POS',
};

export const accumsMap = {
  0: 'No - Accums not Ready',
  1: 'Yes - Accums Ready to Send',
};

export const spendingAccMap = {
  1: 'HSA Setup',
  2: 'HRA Setup',
  3: 'No Spending Account Attached',
};

export const networkMap = {
  1: 'Does Not Apply',
  2: 'INN to OON and OON to INN',
  3: 'INN And OON Are Separate (No Cross Accumulation)',
  4: 'OON To INN Only',
};

export const healthcareReformMap = {
  1: 'Grandfathered',
  2: 'Non-Grandfathered',
};

export const benefitPlanIndMap = {
  1: 'Copay/Coinsurance',
  2: 'HDHP',
};

export const accumTransferMap = {
  2: 'ESI Carrier',
  4: 'ESI Contract',
};

export const medicalIntMap = {
  1: 'Middle',
  2: 'Non-Middle',
};

export const carryoverPhaseMap = {
  0: 'No',
  1: 'Yes',
  2: 'Fourth Quarter',
};

export const benefitPeriodLengthMap = {
  1: '12 Months (Standard)',
  2: 'Lifetime',
  3: 'Other',
};

export const penaltiesApplyMap = {
  0: 'No',
  1: 'Yes',
  2: 'View on Benefit',
};

export const drugTypeStatusMap = {
  1: 'All (Standard)',
  2: 'Brand Only',
  3: 'Generic Only',
};

export const formularyStatusMap = {
  1: 'Formulary and Non Formulary (Standard)',
  2: 'Formulary Drugs Only',
  3: 'Non Formulary Drugs Only',
};

export const otherAccumType = {
  1: 'Deductible',
  2: 'Maximum Out of Pocket',
  3: 'Drug List Specific Cap',
  4: 'Lifetime Cap',
  5: 'Benefit Cap',
};

export const cdhClassCodeMap = {
  1: 'INFERTILIT',
  2: 'SMOKINGCES',
  3: 'CHEMOSPLTY',
  4: 'CHEMOTHERA',
  5: 'DIABETES',
  6: 'MENTALHLTH',
  7: 'SPECIALTY',
};

export const integratedMap = {
  1: 'Integrated',
  2: 'Separate',
};

export const embeddedMap = {
  1: 'Embedded',
  2: 'Non-Embedded',
};
