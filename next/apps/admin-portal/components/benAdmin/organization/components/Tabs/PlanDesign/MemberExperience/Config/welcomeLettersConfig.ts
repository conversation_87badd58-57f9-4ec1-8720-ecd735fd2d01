// welcomeLettersConfig.ts

import { getPropertyPath } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';

import { BASE_PATHS } from './configs';

// Define the field names without the full paths
const WELCOME_LETTERS_FIELDS = {
  member_materials_name: 'member_materials_name',
  member_packets_ind: 'member_packets_ind',
  disruption_letters_ind: 'disruption_letters_ind',
  specialty_letters_ind: 'specialty_letters_ind',
  mail_order_letters_ind: 'mail_order_letters_ind',
  allow_customization_ind: 'allow_customization_ind',
  marketing_outreach_home_delivery_ind: 'marketing_outreach_home_delivery_ind',
  marketing_outreach_specialty_service_ind:
    'marketing_outreach_specialty_service_ind',
  notes: 'notes',
};

/**
 * WelcomeLettersConfig - Type definition for the flattened welcome letters configuration.
 */
export interface WelcomeLettersConfig {
  member_materials_name: string;
  member_packets_ind: string;
  disruption_letters_ind: string;
  specialty_letters_ind: string;
  mail_order_letters_ind: string;
  allow_customization_ind: string;
  marketing_outreach_home_delivery_ind: string;
  marketing_outreach_specialty_service_ind: string;
  notes: string;
}

// Generate the full paths
export const welcomeLettersConfig: WelcomeLettersConfig = Object.entries(
  WELCOME_LETTERS_FIELDS
).reduce(
  (config, [key, value]) => ({
    ...config,
    [key]: getPropertyPath(BASE_PATHS.PLAN_MATERIAL, value),
  }),
  {} as WelcomeLettersConfig
);

/**
 * getWelcomeLettersPath
 * Returns a single path for the given field.
 */
export function getWelcomeLettersPath(
  field: keyof typeof welcomeLettersConfig
): string {
  return welcomeLettersConfig[field];
}
