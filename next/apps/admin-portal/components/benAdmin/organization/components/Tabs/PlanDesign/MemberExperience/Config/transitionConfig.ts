// transitionConfig.ts

import { getPropertyPath } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';

import { BASE_PATHS } from './configs';

// Define the field names without the full paths
const TRANSITION_FIELDS = {
  historical_claims_ind: 'historical_claims_ind',
  ort_transition_file_ind: 'ort_transition_file_ind',
  mail_order_percentage: 'mail_order_percentage',
  pa_file_ind: 'pa_file_ind',
  outbound_claims_file: 'outbound_claims_file',
  carrier_to_carrier_ind: 'carrier_to_carrier_ind',
  patient_transfer_ind: 'patient_transfer_ind',
  accum_transfer_ind: 'accum_transfer_ind',
  follow_me_ind: 'follow_me_ind',
  notes: 'notes',
};

/**
 * TransitionConfig - Type definition for flattened transition field paths.
 */
export interface TransitionConfig {
  historical_claims_ind: string;
  ort_transition_file_ind: string;
  mail_order_percentage: string;
  pa_file_ind: string;
  outbound_claims_file: string;
  carrier_to_carrier_ind: string;
  patient_transfer_ind: string;
  accum_transfer_ind: string;
  follow_me_ind: string;
  notes: string;
}

// Generate the full paths
export const transitionConfig: TransitionConfig = Object.entries(
  TRANSITION_FIELDS
).reduce(
  (config, [key, value]) => ({
    ...config,
    [key]: getPropertyPath(BASE_PATHS.PLAN_TRANSITION, value),
  }),
  {} as TransitionConfig
);

/**
 * getTransitionPath
 * Returns a single path for the given field.
 */
export function getTransitionPath(
  field: keyof typeof transitionConfig
): string {
  return transitionConfig[field];
}
