import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistInterface';
import { formatDate } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Table/utils/tableUtils';
import { TemplateFieldConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';

import { implementationConfig } from '../Config/implementationConfig';
export const getImplementationInformationFields = (
  orgDetails: Partial<OrganizationDetails>,
  maps: Partial<PicklistMaps>
): Partial<TemplateFieldConfig>[] => [
  {
    label: 'Implementation Start Date',
    name: implementationConfig.implementation_start_date,
    value: formatDate(orgDetails?.plan?.implementation_start_date),
  },
  {
    label: 'Implementation Timeline',
    name: implementationConfig.implementation_timeline,
    value: orgDetails?.plan?.implementation_timeline,
    type: 'dropdownSelect',
    optionsMap: maps.implementationTimelineMap,
  },
];
