export const responsibleMap: Record<number, string> = {
  1: 'Medical Vendor',
  2: 'Other (Medical Vendor, TPA, etc)',
  3: 'PBM',
  4: 'PBM/Medical Vendor',
  5: 'RxBenefits',
  6: 'TPA',
};

export const idCardTypeMap: Record<number, string> = {
  1: 'Separate',
  2: 'Combined',
  3: 'Separate/Combined',
};

export const logoMap: Record<number, string> = {
  0: 'Yes',
  1: 'No',
  2: 'Not Applicable',
};

export const altIdMap: Record<number, string> = {
  0: 'Client ID',
  1: 'Client ID/Generated ID',
  2: 'Generated ID',
  3: 'Social Security Number',
};

export const mailingMap: Record<number, string> = {
  0: "Member's Home",
  1: 'Group HR Office',
};

export const yesNoMap: Record<number, string> = {
  0: 'No',
  1: 'Yes',
};

export const yesNoEsiMap: Record<number, string> = {
  0: 'No',
  1: 'Yes',
  2: 'ESI PPT',
};

export const mailMap: Record<number, string> = {
  1: 'Bulk',
  2: 'Electronic',
  3: 'Electronic and Mail',
  4: 'Mail',
};
