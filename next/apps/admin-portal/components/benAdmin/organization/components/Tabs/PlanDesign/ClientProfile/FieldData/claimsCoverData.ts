import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistInterface';
import { TemplateFieldConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';

import { yesNoMap } from './maps';

export const getClaimsCoverFields = (
  orgDetails: Partial<OrganizationDetails>,
  maps: Partial<PicklistMaps>
): Partial<TemplateFieldConfig>[] => {
  const planPdx = orgDetails?.plan?.plan_pdx;

  return [
    {
      label: 'Cover Retail Claims',
      name: 'retail_claims_covered_ind',
      value: planPdx?.retail_claims_covered_ind,
      type: 'dropdownSelect',
      optionsMap: yesNoMap,
    },
    {
      label: 'Cover Mail Claims',
      name: 'mail_claims_covered_ind',
      value: planPdx?.mail_claims_covered_ind,
      type: 'dropdownSelect',
      optionsMap: yesNoMap,
    },
    {
      label: 'Cover Paper Claims',
      name: 'paper_claims_covered_ind',
      value: planPdx?.paper_claims_covered_ind,
      type: 'dropdownSelect',
      optionsMap: yesNoMap,
    },
    {
      label: 'Cover Out of Network',
      name: 'out_of_network_claims_covered_ind',
      value: planPdx?.out_of_network_claims_covered_ind,
      type: 'dropdownSelect',
      optionsMap: yesNoMap,
    },
    {
      label: 'Cover Foreign Claims',
      name: 'foreign_claims_covered',
      value: planPdx?.foreign_claims_covered,
      type: 'input',
    },
    {
      label: 'Allow for Coordination of Benefits (COB)',
      name: 'cob_allowed_ind',
      value: planPdx?.cob_allowed_ind,
      type: 'dropdownSelect',
      optionsMap: maps.cobAllowedMap,
    },
    {
      label: 'Medicaid Subrogation Claims',
      name: 'medicaid_subrogation_ind',
      value: planPdx?.medicaid_subrogation_ind,
      type: 'dropdownSelect',
      optionsMap: maps.medicaidSubrogationMap,
    },
    {
      label: 'Paper Claims Covered - Pricing',
      name: 'paper_claims_pricing_ind',
      value: planPdx?.paper_claims_pricing_ind,
      type: 'dropdownSelect',
      optionsMap: maps.paperClaimsPricingMap,
    },
  ];
};
