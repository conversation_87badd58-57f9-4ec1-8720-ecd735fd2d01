// getTransitionFields.ts
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistInterface';
import { TemplateFieldConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';

import { transitionConfig } from '../Config/transitionConfig'; // Adjust path as needed
/**
 * getTransitionFields - Generates an array of TemplateFieldConfig for transition fields.
 *
 * It uses a centralized configuration (transitionConfig) for the dot-notated field names.
 *
 * @param formData - The organization details containing transition data.
 * @returns Array of field configurations for the transition section.
 */
export const getTransitionFields = (
  formData: OrganizationDetails,
  maps: Partial<PicklistMaps>
): Partial<TemplateFieldConfig>[] => {
  const planTransition = formData?.plan?.plan_transition;
  return [
    {
      label: '6 Months Claims Files',
      value: planTransition?.historical_claims_ind,
      name: transitionConfig.historical_claims_ind,
      type: 'dropdownSelect',
      optionsMap: maps.yesNoMap,
    },
    {
      label: 'ORT Transition File',
      value: planTransition?.ort_transition_file_ind,
      name: transitionConfig.ort_transition_file_ind,
      type: 'dropdownSelect',
      optionsMap: maps.yesNoMap,
    },
    {
      label: 'Mail Order Percentage',
      value: planTransition?.mail_order_percentage,
      name: transitionConfig.mail_order_percentage,
    },
    {
      label: 'PA File',
      value: planTransition?.pa_file_ind,
      name: transitionConfig.pa_file_ind,
      type: 'dropdownSelect',
      optionsMap: maps.yesNoMap,
    },
    {
      label: 'Outbound Claims Files',
      value: planTransition?.outbound_claims_file,
      name: transitionConfig.outbound_claims_file,
    },
    {
      label: 'Carrier to Carrier Process',
      value: planTransition?.carrier_to_carrier_ind,
      name: transitionConfig.carrier_to_carrier_ind,
      type: 'dropdownSelect',
      optionsMap: maps.yesNoMap,
    },
    {
      label: 'Patient Transfer Process',
      value: planTransition?.patient_transfer_ind,
      name: transitionConfig.patient_transfer_ind,
      type: 'dropdownSelect',
      optionsMap: maps.yesNoMap,
    },
    {
      label: 'Accum Transfer',
      value: planTransition?.accum_transfer_ind,
      name: transitionConfig.accum_transfer_ind,
      type: 'dropdownSelect',
      optionsMap: maps.accumTransferMap,
    },
    {
      label: 'Follow Me Logic Process',
      value: planTransition?.follow_me_ind,
      name: transitionConfig.follow_me_ind,
      type: 'dropdownSelect',
      optionsMap: maps.yesNoMap,
    },
    {
      label: 'Transition Notes',
      value: planTransition?.notes,
      name: transitionConfig.notes,
    },
  ];
};
