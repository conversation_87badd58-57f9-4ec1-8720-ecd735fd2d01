import { UseFormReturn } from 'react-hook-form';

import { usePicklistMaps } from '../../../../maps/picklistMaps';
import {
  ID_CARDS_ITEM,
  MEMBER_SERVICES_ITEM,
  TRANSITION_FILES_AND_DETAILS_ITEM,
  WELCOME_KIT_AND_LETTERS_ITEM,
} from '../../../ChangeRequestIntake/Navigation/navigationConstants';
import { getIdCardFields } from './FieldGroups/idCardFieldData';
import { getMemberServicesFields } from './FieldGroups/memberServiceData';
import { getTransitionFields } from './FieldGroups/transitionFieldData';
import { getWelcomeLettersFields } from './FieldGroups/welcomeLettersFieldData';

export const useMemberExperienceFieldGroups = (
  formMethods: UseFormReturn<any> // Accept the form instance from OrganizationView
) => {
  const { handleSubmit, watch } = formMethods;

  const maps = usePicklistMaps();

  const memeberServiceFields = getMemberServicesFields(watch());

  return {
    memberExperience: {
      idCardGroup: {
        subtitle: 'ID Cards',
        fields: getIdCardFields(watch()),
        columns: 3,
        handleSubmit,
        formMethods,
        editable: true,
        tab: ID_CARDS_ITEM,
      },
      transitionGroup: {
        subtitle: 'Transition Files and Details',
        fields: getTransitionFields(watch(), maps),
        columns: 4,
        handleSubmit,
        formMethods,
        editable: true,
        tab: TRANSITION_FILES_AND_DETAILS_ITEM,
      },
      welcomeLettersGroup: {
        subtitle: 'Welcome Kit & Letters',
        fields: getWelcomeLettersFields(watch(), maps),
        columns: 2,
        handleSubmit,
        formMethods,
        editable: true,
        tab: WELCOME_KIT_AND_LETTERS_ITEM,
      },
      memberServicesGroup: {
        subtitle: 'Member Services Providers',
        fields: memeberServiceFields,
        columns: 2,
        handleSubmit,
        formMethods,
        editable: true,
        tab: MEMBER_SERVICES_ITEM,
      },
    },
  };
};
