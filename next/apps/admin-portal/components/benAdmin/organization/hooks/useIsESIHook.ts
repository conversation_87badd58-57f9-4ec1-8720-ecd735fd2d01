import { useMemo } from 'react';
import { UseFormReturn } from 'react-hook-form';

/**
 * Custom hook to determine if the current product is an Express Scripts product
 * @param formMethods The form methods object from react-hook-form
 * @param productNamePath Optional path to the product name field (defaults to 'plan.product.vendor.pbm.legal_entity.name')
 * @returns Boolean indicating if this is an ESI product
 */
export const useIsESIProduct = (formMethods: UseFormReturn<any>): boolean => {
  // Watch the product name field
  const productName =
    formMethods.getValues('plan.product.vendor.pbm.legal_entity.name') ||
    formMethods.getValues('plan.product.vendor.name');

  // Check if this is an ESI product
  const isESIProduct = useMemo(
    () =>
      typeof productName === 'string' &&
      productName.includes('Express Scripts'),
    [productName]
  );

  return isESIProduct;
};
