import { useMemo } from 'react';

/**
 * Custom hook to determine if Feature Flag SHOW_BEN_ADMIN_V2 is enabled.
 *
 * Logic:
 * - If the env var is undefined, default to true (feature enabled).
 * - If the env var equals the string "true" (case-insensitive), return true.
 * - Otherwise, return false.
 *
 * Usage:
 * const { flag } = useBenAdminV2();
 */
export function useBenAdminV2(): { flag: boolean } {
  const flag = useMemo(() => {
    const raw = process.env.SHOW_BEN_ADMIN_V2;

    // Default to enabled when not explicitly set
    if (raw === undefined) {
      return true;
    }

    // Treat any variation of "true" (case-insensitive) as enabled
    return raw.toLowerCase() === 'true';
  }, []);

  return { flag };
}
