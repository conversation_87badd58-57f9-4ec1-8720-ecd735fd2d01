{"extends": "../../tsconfig.base.json", "compilerOptions": {"rootDir": "../..", "jsx": "preserve", "allowJs": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "resolveJsonModule": true, "isolatedModules": true, "incremental": true, "plugins": [{"name": "next"}], "types": ["jest", "node"]}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "../../apps/admin-portal/.next/types/**/*.ts", "../../dist/apps/admin-portal/.next/types/**/*.ts", "next-env.d.ts", "../../libs/shared/api/src/lib/useApi.ts", ".next/types/**/*.ts", "../../dist/.next/types/**/*.ts", "../../dist/admin-portal/.next/types/**/*.ts"], "exclude": ["node_modules", "jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts"]}