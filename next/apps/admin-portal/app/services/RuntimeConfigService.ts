// --------------------------------------
// File: RuntimeConfigService.ts
// --------------------------------------
import axios from 'axios';

/**
 * Interface for complete runtime configuration
 */
export interface RuntimeConfig {
  env: string;
  envVariables: Record<string, string>;
}

/**
 * Singleton service for managing runtime configuration.
 * Provides access to environment-specific settings and API endpoints.
 */
class RuntimeConfigService {
  private static instance: RuntimeConfigService;
  private config: RuntimeConfig | null = null;
  private initPromise: Promise<RuntimeConfig | null> | null = null;

  private constructor() {
    // Private constructor to enforce singleton pattern
  }

  /**
   * Get the singleton instance of the RuntimeConfigService
   */
  public static getInstance(): RuntimeConfigService {
    if (!RuntimeConfigService.instance) {
      RuntimeConfigService.instance = new RuntimeConfigService();
    }
    return RuntimeConfigService.instance;
  }

  /**
   * Get the runtime configuration.
   * Loads configuration on first call and caches for subsequent calls.
   */
  public async getConfig(): Promise<RuntimeConfig | null> {
    if (this.config) {
      return this.config;
    }

    if (!this.initPromise) {
      this.initPromise = this.initializeConfig();
    }

    return this.initPromise;
  }

  /**
   * Get a URL from an environment variable name
   */
  public async getUrlFromEnvVariable(envVariable: string): Promise<string> {
    const config = await this.getConfig();
    if (!config) return '';

    const url = config.envVariables[envVariable] || '';
    // Clean the URL by:
    // 1. Removing any quotes (single or double)
    // 2. Removing trailing commas
    // 3. Ensuring proper URL formatting
    return url
      .replace(/['"]/g, '') // Remove quotes
      .replace(/,\s*$/, '') // Remove trailing commas
      .replace(/\/+$/, '') // Remove trailing slashes
      .trim();
  }

  /**
   * Initialize configuration by fetching from the API
   */
  private async initializeConfig(): Promise<RuntimeConfig | null> {
    try {
      const response = await axios.get('/api/getConfig');
      this.config = response.data;
      return this.config;
    } catch (error) {
      console.error('Failed to load runtime configuration', error);

      // Fallback configuration using direct environment variables
      this.config = {
        env: 'development',
        envVariables: this.getEnvironmentVariables(),
      };

      return this.config;
    }
  }

  /**
   * Get all relevant environment variables
   */
  private getEnvironmentVariables(): Record<string, string> {
    const variables: Record<string, string> = {};

    // Get all environment variables that start with NEXT_PUBLIC_ and end with _URL
    Object.entries(process.env).forEach(([key, value]) => {
      if (key.startsWith('NEXT_PUBLIC_') && key.endsWith('_URL')) {
        variables[key] = value || '';
      }
    });

    return variables;
  }

  /**
   * Get the current environment (qa, stage, prod, etc.)
   */
  public getEnvironment(): string {
    return this.config?.env || 'development';
  }
}

export default RuntimeConfigService;
