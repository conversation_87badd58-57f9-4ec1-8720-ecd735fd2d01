import { useMutation, useQueries } from '@tanstack/react-query';
import axios from 'axios';

import { ApiDataMap } from '../_interfaces/benAdmin';
import { apiConfig } from '../api/apiMaps';
// Utility function to get endpoint URL from the apiConfig map
const getEndpointFromMap = (
  key: string,
  pathParams: Record<string, any> = {}
) => {
  const configItem = apiConfig[key];
  if (!configItem) {
    throw new Error(`No configuration found for key: ${key}`);
  }

  return typeof configItem.api === 'function'
    ? configItem.api(...Object.values(pathParams))
    : configItem.api;
};

const buildFullUrl = (key: string, pathParams = {}, queryParams = {}) => {
  const endpoint = getEndpointFromMap(key, pathParams);

  const queryString = new URLSearchParams(
    Object.entries(queryParams).reduce((acc, [key, value]) => {
      acc[key] = String(value);
      return acc;
    }, {} as Record<string, string>)
  ).toString();

  const fullUrl = queryString ? `${endpoint}?${queryString}` : endpoint;
  return fullUrl;
};

// Generic API query configuration type
type KnownApiKeys = keyof ApiDataMap; // Extract valid API keys from ApiDataMap
type QueryConfig<K extends KnownApiKeys> = {
  key: K;
  pathParams?: Record<string, any>;
  queryParams?: Record<string, any>;
  options?: any;
};

// Hook for multiple API queries using React Query
const useApiQuery = <TConfigs extends QueryConfig<KnownApiKeys>[]>(
  queries: [...TConfigs]
): {
  isLoading: boolean;
  isError: boolean;
  refetch: { [K in TConfigs[number]['key']]: () => Promise<any> };
} & { [K in TConfigs[number]['key']]: ApiDataMap[K] } => {
  const results = useQueries({
    queries: queries.map(
      ({ key, pathParams = {}, queryParams = {}, options = {} }) => {
        return {
          queryKey: [key, pathParams, queryParams],
          queryFn: async () => {
            const urlWithParams = buildFullUrl(key, pathParams, queryParams);

            try {
              const { data } = await axios.get(urlWithParams, {
                headers: { 'X-Request-Key': key },
              });
              return data;
            } catch (error) {
              console.error(`[useApiQuery] Error for ${key}:`, error);
              throw error;
            }
          },
          ...options,
        };
      }
    ),
  });

  const isLoading = results.some((result) => result.isLoading);
  const isError = results.some((result) => result.isError);

  const data = queries.reduce((acc, { key }, index) => {
    acc[key as TConfigs[number]['key']] = results[index]
      ?.data as ApiDataMap[TConfigs[number]['key']];
    return acc;
  }, {} as { [K in TConfigs[number]['key']]: ApiDataMap[K] });

  const refetchFunctions = queries.reduce((acc, { key }, index) => {
    acc[key as TConfigs[number]['key']] = results[index]?.refetch;
    return acc;
  }, {} as { [K in TConfigs[number]['key']]: () => Promise<any> });

  return {
    isLoading,
    isError,
    refetch: refetchFunctions,
    ...data,
  };
};

// Hook for API mutations (POST, PUT, DELETE)
const useApiMutation = <TData = any>(
  key: string,
  method: string,
  options: any = {}
) => {
  return useMutation<
    any,
    Error,
    TData & {
      pathParams?: Record<string, any>;
      queryParams?: Record<string, any>;
    }
  >({
    mutationFn: async (params) => {
      // Extract the actual data body from the params
      const { pathParams = {}, queryParams = {}, ...body } = params || {};

      // Build the URL with the path params provided at execution time
      const urlWithParams = buildFullUrl(key, pathParams, queryParams);

      const { data } = await axios({
        method,
        url: urlWithParams,
        data: body,
        headers: { 'X-Request-Key': key },
      });
      return data;
    },
    ...options,
  });
};

// Exporting hooks through a custom hook wrapper
export const useBenAdmin = () => ({
  useApiQuery,
  useApiMutation,
});
