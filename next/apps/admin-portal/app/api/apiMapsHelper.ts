// --------------------------------------
// File: apiMapsHelper.ts
// --------------------------------------
import RuntimeConfigService from '../services/RuntimeConfigService';

/**
 * Enum for available API services - easily extensible
 */
export enum ApiService {
  Protect = 'protect',
  BenAdmin = 'benAdmin',
  // Add new services here
}

/**
 * Interface for service configuration
 */
export interface ServiceConfig {
  apiBase: string;
  envVariable: string; // Direct reference to the environment variable
}

/**
 * Configuration mapping for services - centralized configuration
 * Developers only need to add new services here with their env variable
 */
const serviceConfig: Record<ApiService, ServiceConfig> = {
  [ApiService.Protect]: {
    apiBase: '/api/protect-service',
    envVariable: 'NEXT_PUBLIC_PROTECT_URL',
  },
  [ApiService.BenAdmin]: {
    apiBase: '/api/ben-admin',
    envVariable: 'NEXT_PUBLIC_BEN_ADMIN_URL',
  },
  // Add new services here following the same pattern
};

/**
 * Interface for API configuration item
 */
export type ApiConfigItem = {
  api: (id?: number) => string;
  url: (params?: any) => Promise<string>;
};

/**
 * Builds a path with optional parameters
 */
function buildPath(
  base: string,
  path: string,
  params?: Record<string, any>,
  idKey = 'id'
): string {
  // Clean the base URL by removing trailing slashes
  const cleanBase = base.replace(/\/+$/, '');
  // Clean the path by removing leading slashes
  const cleanPath = path.replace(/^\/+/, '');

  if (cleanPath.includes(':') && params) {
    let updatedPath = cleanPath;
    for (const [key, value] of Object.entries(params)) {
      if (updatedPath.includes(`:${key}`)) {
        updatedPath = updatedPath.replace(`:${key}`, value.toString());
      }
    }
    return `${cleanBase}/${updatedPath}`;
  }

  // Original logic for simple id paths
  if (params && params[idKey]) {
    return `${cleanBase}/${cleanPath}/${params[idKey]}`;
  }

  return `${cleanBase}/${cleanPath}`;
}

/**
 * Creates an API configuration item
 */
export function createApi(
  baseApi: string,
  envVariable: string,
  path: string,
  idKey = 'id'
): ApiConfigItem {
  return {
    api: (id?: number) => {
      const builtPath = buildPath(baseApi, path, { [idKey]: id }, idKey);
      return builtPath;
    },
    url: async (params?: any) => {
      // Get runtime configuration service
      const configService = RuntimeConfigService.getInstance();
      const urlBase = await configService.getUrlFromEnvVariable(envVariable);

      if (!urlBase) {
        console.error(
          `[createApi.url] No URL base found for env variable: ${envVariable}`
        );
        return '';
      }

      const finalUrl = buildPath(urlBase, path, params, idKey);
      return finalUrl;
    },
  };
}

/**
 * Generates API configuration from raw config
 */
export function generateApiConfig(
  rawConfig: Record<string, [ApiService, string]>
): Record<string, ApiConfigItem> {
  const result: Record<string, ApiConfigItem> = {};

  for (const [key, [service, path]] of Object.entries(rawConfig)) {
    const { apiBase, envVariable } = serviceConfig[service];
    result[key] = createApi(apiBase, envVariable, path);
  }
  return result;
}
