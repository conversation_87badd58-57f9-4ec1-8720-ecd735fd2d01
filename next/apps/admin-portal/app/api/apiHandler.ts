//apiHandler

import { getAccessToken, withApiAuthRequired } from '@auth0/nextjs-auth0';
import { NextRequest, NextResponse } from 'next/server';

import { apiConfig } from './apiMaps';

/**
 * Utility to fetch data based on the method and keys
 */
const fetchData = async (
  key: string,
  method: string,
  req: NextRequest,
  params: any
) => {
  const res = new NextResponse();
  const { accessToken } = await getAccessToken(req, res);

  const urlConfig = apiConfig[key];
  if (!urlConfig) {
    console.error(`[fetchData] No configuration found for key: ${key}`);
    throw new Error(`No configuration found for key: ${key}`);
  }

  const { searchParams } = req.nextUrl;
  const contentType = req.headers.get('content-type');

  // URL will now be resolved at runtime asynchronously
  let url =
    typeof urlConfig.url === 'function'
      ? await urlConfig.url(params)
      : urlConfig.url;

  console.log(`[fetchData] Resolved URL: ${url}`);
  console.log(`[fetchData] Environment variables:`, {
    NEXT_PUBLIC_BEN_ADMIN_URL: process.env.NEXT_PUBLIC_BEN_ADMIN_URL,
  });

  const headers: Record<string, string> = {
    ...(accessToken ? { Authorization: `Bearer ${accessToken}` } : {}),
    'Content-Type': contentType ?? 'application/json',
  };

  if (searchParams) {
    url += `?${searchParams?.toString()}`;
  }

  // send correct data format based on content-type here
  let body;
  if (method !== 'GET') {
    if (req.headers.get('content-type')?.includes('multipart/form-data')) {
      body = await req.blob();
    } else {
      const jsonData = await req.json();
      body = JSON.stringify(jsonData);
    }
  }

  const fetchOptions = {
    method,
    headers,
    ...(body && { body }),
  };

  const response = await fetch(url!, fetchOptions);

  if (!response.ok) {
    console.error(`[fetchData] Request failed with status ${response.status}`);
    throw new Error(`Request failed with status ${response.status}`);
  }

  // For file downloads since they're binary, not json
  if (
    response.headers.get('content-type')?.includes('application/octet-stream')
  ) {
    return await response.blob();
  } else {
    return await response.json();
  }
};

/**
 * Flattens nested params objects
 */
const getFlattenedParams = (params: any): any => {
  while (params && params.params) {
    params = params.params;
  }
  return params;
};

/**
 * Generic handler that processes all methods dynamically
 */
export const apiHandler = withApiAuthRequired(
  async (req: NextRequest, params: any) => {
    try {
      const flattenedParams = getFlattenedParams(params);
      const method = req.method?.toUpperCase();
      const key = req.headers.get('X-Request-Key');

      if (!method || !key) {
        throw new Error('Invalid request: missing method or key');
      }
      const result = await fetchData(key, method, req, flattenedParams);
      // A file cannot be JSON, needs to be a blob. Send that according to type
      if (result.type === 'application/octet-stream') {
        return new NextResponse(result, { status: 200, statusText: 'OK' });
      }
      return NextResponse.json(result);
    } catch (error: any) {
      return NextResponse.json(
        { error: error.message },
        { status: error.status || 500 }
      );
    }
  }
);

/**
 * Function to create handlers for each HTTP method
 */
export const createHandler = (method: string) => {
  return (req: NextRequest, params: any) => {
    if (req.method !== method) {
      return NextResponse.json(
        { error: `Method ${req.method} not allowed` },
        { status: 405 }
      );
    }
    return apiHandler(req, params);
  };
};
