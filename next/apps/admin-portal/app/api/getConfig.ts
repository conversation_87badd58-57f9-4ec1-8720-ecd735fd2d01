// --------------------------------------
// File: getConfig.ts
// --------------------------------------
import { NextApiRequest, NextApiResponse } from 'next';

/**
 * API endpoint to retrieve environment-specific configuration.
 * This determines the current environment and returns appropriate configuration values.
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Determine environment from hostname or headers
  const host = req.headers.host || '';
  let env = 'development';

  if (host.includes('prod') || host.includes('www')) {
    env = 'prod';
  } else if (host.includes('stage')) {
    env = 'stage';
  } else if (host.includes('qa')) {
    env = 'qa';
  } else if (host.includes('demo')) {
    env = 'demo';
  }

  // Get all environment variables that start with NEXT_PUBLIC_ and end with _URL
  const envVariables: Record<string, string> = {};
  Object.entries(process.env).forEach(([key, value]) => {
    if (key.startsWith('NEXT_PUBLIC_') && key.endsWith('_URL')) {
      envVariables[key] = value || '';
    }
  });

  // Return comprehensive configuration
  return res.status(200).json({
    env,
    envVariables,
  });
}
