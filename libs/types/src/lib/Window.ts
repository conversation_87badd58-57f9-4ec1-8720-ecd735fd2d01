import { Auth0ClientOptions } from '@auth0/auth0-spa-js';
import { RumInitConfiguration } from '@datadog/browser-rum';

export interface RxbWindow extends Window {
  _rxbenv_: RxbEnv;
}

export interface RxbEnv {
  ENV: string;
  APIURI?: string;
  AUDITAPIURI?: string;
  AUTHAPIURI?: string;
  DATAMAPPERAPIURI?: string;
  DRUGSEARCHAPIURI?: string;
  ELIGIBILITYIMPORTAPIURI?: string;
  NEXT_PUBLIC_BEN_ADMIN_URL?: string;
  SHOW_BEN_ADMIN_V2?: string;
  EVALUATEAPIURI?: string;
  PROTECTAPIURI?: string;
  SALESFORCEAPIURI?: string;
  MEMBERISSUESAPIURI?: string;
  MEMBERSEARCHAPIURI?: string;
  PLANPROVISIONAPIURI?: string;
  COMMUNICATIONMANAGERAPIURI?: string;
  MEMBERSHUBAPIURI?: string;
  REFDATAAPIURI?: string;
  TASKSERVICEAPIURI?: string;
  FMSINVOICEAPIURI?: string;
  BILLINGAPIURI?: string;
  OKTA_PKCE: string;
  OKTA_CLIENTID: string;
  OKTA_REDIRECTURI: string;
  OKTA_ISSUER: string;
  OKTA_TOKENMANAGER_STORAGE: string;
  OKTA_TOKENMANAGER_AUTORENEW: string;
  OKTA_COOKIES_SECURE: string;
  OKTA_MAXCLOCKSKEW: string;
  SESSION_TIMEOUT: string;
  STALE_TIME?: string;
  DATADOG_APPLICATION_ID?: string;
  DATADOG_CLIENT_TOKEN?: string;
  DATADOG_SAMPLE_RATE?: string;
  DATADOG_APPLICATION_VERSION?: string;
  DATANET_LOGIN_URL?: string;
  PORTAL_LOGIN_URL?: string;
  FEEDBACK_FORM?: string;
  STATIC_ASSETS_URL?: string;
  HOME_ROUTE: string;
  WRITE_SCHEMA_ID: number;
  IMPORT_SCHEMA_WRITE_SCHEMA_ID: number;
  UNMAPPED_EDI_TEMPLATE_ID: number;
  QLIK_APP_ID: string;
  QLIK_SHEET_ID: string;
  AUTH0_ISSUER_BASE_URL: string;
  OPTIMIZE_AUTH0_CLIENT_ID: string;
  AUTH0_REDIRECTURI: string;
}

export interface PortalConfig {
  apiUri: string;
  auditUri: string;
  authApiUri: string;
  evaluateApiUri: string;
  dataMapperApiUri: string;
  drugSearchApiUri: string;
  protectApiUri: string;
  eligibilityImportApiUri: string;
  benAdminUri: string;
  benAdminV2: string;
  fmsInvoiceApiUri: string;
  billingApiUri: string;
  salesforceApiUri: string;
  memberIssuesApiUri: string;
  memberSearchApiUri: string;
  planProvisionApiUri: string;
  communicationManagerApiUri: string;
  membersHubApiUri: string;
  refDataApiUri: string;
  applicationName: string;
  taskServiceApiUri: string;
  env: string;
  sessionTimeout: number;
  auth0: Auth0ClientOptions;
  datadog: RumInitConfiguration;
  staleTime: number;
  dataNetLoginUrl: string;
  portalLoginUrl: string;
  feedbackForm: string;
  staticAssetsUrl: string;
  homeRoute: string;
  writeSchemaId: number;
  importSchemaWriteSchemaId: number;
  unmappedEdiTemplateId: number;
  qlikAppId: string;
  qlikSheetId: string;
}
