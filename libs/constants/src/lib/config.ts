import { PortalConfig, RxbWindow } from '@optimize/types';

declare let window: RxbWindow;

export const config: PortalConfig = {
  apiUri: window._rxbenv_?.APIURI ?? '',
  billingApiUri: window._rxbenv_?.BILLINGAPIURI ?? '',
  fmsInvoiceApiUri: window._rxbenv_?.FMSINVOICEAPIURI ?? '',
  auditUri: window._rxbenv_?.AUDITAPIURI ?? '',
  taskServiceApiUri: window._rxbenv_?.TASKSERVICEAPIURI ?? '',
  authApiUri: window._rxbenv_?.AUTHAPIURI ?? '',
  dataMapperApiUri: window._rxbenv_?.DATAMAPPERAPIURI ?? '',
  drugSearchApiUri: window._rxbenv_?.DRUGSEARCHAPIURI ?? '',
  eligibilityImportApiUri: window._rxbenv_?.ELIGIBILITYIMPORTAPIURI ?? '',
  benAdminUri: window._rxbenv_?.NEXT_PUBLIC_BEN_ADMIN_URL ?? '',
  benAdminV2: window._rxbenv_?.SHOW_BEN_ADMIN_V2 ?? '',
  evaluateApiUri: window._rxbenv_?.EVALUATEAPIURI ?? '',
  protectApiUri: window._rxbenv_?.PROTECTAPIURI ?? '',
  salesforceApiUri: window._rxbenv_?.SALESFORCEAPIURI ?? '',
  memberIssuesApiUri: window._rxbenv_?.MEMBERISSUESAPIURI ?? '',
  memberSearchApiUri: window._rxbenv_?.MEMBERSEARCHAPIURI ?? '',
  planProvisionApiUri: window._rxbenv_?.PLANPROVISIONAPIURI ?? '',
  communicationManagerApiUri: window._rxbenv_?.COMMUNICATIONMANAGERAPIURI ?? '',
  membersHubApiUri: window._rxbenv_?.MEMBERSHUBAPIURI ?? '',
  refDataApiUri: window._rxbenv_?.REFDATAAPIURI ?? '',
  applicationName: 'Optimize',
  env: window._rxbenv_.ENV,
  sessionTimeout: JSON.parse(window._rxbenv_?.SESSION_TIMEOUT ?? 30000) || 300000,
  staleTime: JSON.parse(window._rxbenv_?.STALE_TIME ?? '90000') || 90000,
  auth0: {
    domain: window._rxbenv_.AUTH0_ISSUER_BASE_URL ?? `https://rxbenefits-qa.us.auth0.com`,
    clientId: window._rxbenv_.OPTIMIZE_AUTH0_CLIENT_ID ?? 'Q01DJblbd5VxigGC0FnMugs6tECJ8GGY',
    authorizationParams: {
      redirect_uri: window._rxbenv_.AUTH0_REDIRECTURI,
      audience: window._rxbenv_.AUTH0_ISSUER_BASE_URL + `/api/v2/`,
      scope: 'client_credentials',
    },
  },
  dataNetLoginUrl: window._rxbenv_.DATANET_LOGIN_URL ?? '',
  portalLoginUrl: window._rxbenv_.PORTAL_LOGIN_URL ?? '',
  datadog: {
    applicationId: window._rxbenv_.DATADOG_APPLICATION_ID ?? '',
    clientToken: window._rxbenv_.DATADOG_CLIENT_TOKEN ?? '',
    site: 'datadoghq.com',
    service: 'optimize-ui',
    env: window._rxbenv_.ENV ?? 'unknown',
    version: window._rxbenv_.DATADOG_APPLICATION_VERSION,
    sampleRate: parseInt(window._rxbenv_.DATADOG_SAMPLE_RATE ?? '0'),
    replaySampleRate: parseInt(window._rxbenv_.DATADOG_SAMPLE_RATE ?? '0'),
    trackInteractions: true,
    defaultPrivacyLevel: 'mask',
    allowedTracingOrigins: [/https:\/\/.*\.rxbenefits\.cloud/, /https:\/\/.*\.rxbenefits\.com/],
  },
  feedbackForm: window._rxbenv_.FEEDBACK_FORM ?? '',
  staticAssetsUrl: window._rxbenv_.STATIC_ASSETS_URL ?? '',
  homeRoute: window._rxbenv_.HOME_ROUTE ?? '',
  writeSchemaId: window._rxbenv_.WRITE_SCHEMA_ID,
  importSchemaWriteSchemaId: window._rxbenv_.IMPORT_SCHEMA_WRITE_SCHEMA_ID,
  unmappedEdiTemplateId: window._rxbenv_.UNMAPPED_EDI_TEMPLATE_ID,
  qlikAppId: window._rxbenv_.QLIK_APP_ID,
  qlikSheetId: window._rxbenv_.QLIK_SHEET_ID,
};
