#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

# Function to strip ANSI color codes
strip_ansi() {
  sed 's/\x1b\[[0-9;]*m//g' | sed 's/\x1b\[[0-9;]*[a-zA-Z]//g'
}

# Function to convert file paths to clickable links
make_clickable() {
  WORKSPACE_PATH=$(pwd)
  stripped_input=$(echo "$1" | strip_ansi)

  if [[ $stripped_input =~ ^[[:space:]]*($WORKSPACE_PATH/[^:]+) ]]; then
    file_path="${BASH_REMATCH[1]}"
    echo "\033]8;;file://$file_path\033\\$stripped_input\033]8;;\033\\"
  elif [[ $stripped_input =~ ^[[:space:]]*([0-9]+):([0-9]+)[[:space:]]+(error|warning) ]]; then
    line_num="${BASH_REMATCH[1]}"
    # Use the last file path seen
    if [ -n "$prev_file_path" ]; then
      # VS Code supports #L<line> for line numbers
      echo "\033]8;;file://$prev_file_path#L$line_num\033\\$stripped_input\033]8;;\033\\"
    else
      echo "$stripped_input"
    fi
  else
    echo "$stripped_input"
  fi
}

# Function to format lint output for better readability
format_lint_output() {
  # Add visual separators and color
  echo "\n\033[1;31m=== Linting Errors ===\033[0m\n"
  
  local prev_file_path=""
  
  # Process each line of input
  while IFS= read -r line; do
    # Strip ANSI codes for processing
    stripped_line=$(echo "$line" | strip_ansi)
    
    # Check if this is a file path line
    if [[ $stripped_line =~ ^[[:space:]]*$WORKSPACE_PATH/[^:]+$ ]]; then
      prev_file_path="$stripped_line"
      echo "$stripped_line"  # Just print, don't make clickable
    # Check if this is an error/warning line
    elif [[ $stripped_line =~ ^[[:space:]]*[0-9]+:[0-9]+[[:space:]]+(error|warning) ]]; then
      if [[ $stripped_line =~ warning ]]; then
        # Yellow for warnings
        echo "\033[1;33m$(make_clickable "$line")\033[0m"
      else
        # Red for errors
        echo "\033[1;31m$(make_clickable "$line")\033[0m"
      fi
    else
      echo "$line"
    fi
  done
  
  echo "FINISHED"
}

# Function to run lint and capture output
run_lint() {
  local project=$1
  local temp_file=$(mktemp)
  
  # Run lint and capture both stdout and stderr to temp file
  # Use --no-color to reduce ANSI code interference
  npx nx lint $project --fix --no-color > "$temp_file" 2>&1
  local exit_code=$?
  
  # Read and format the output
  if [ $exit_code -ne 0 ]; then
    # Process the output to make file paths and line numbers clickable
    cat "$temp_file" | format_lint_output
  fi
  
  # Clean up
  rm "$temp_file"
  return $exit_code
}

echo "🔍 Running pre-commit checks..."

# Get staged files and separate them properly
STAGED_FILES=$(git diff --staged --name-only)

# Create temporary files to store lists
EXISTING_FILES_LIST=$(mktemp)
DELETED_FILES_LIST=$(mktemp)

# Separate existing files from deleted files using safer approach
echo "$STAGED_FILES" | while IFS= read -r file; do
  if [ -n "$file" ]; then
    if [ -f "$file" ]; then
      echo "$file" >> "$EXISTING_FILES_LIST"
    else
      echo "$file" >> "$DELETED_FILES_LIST"
    fi
  fi
done

# Read back the lists
EXISTING_STAGED_FILES=""
DELETED_FILES=""

if [ -s "$EXISTING_FILES_LIST" ]; then
  EXISTING_STAGED_FILES=$(cat "$EXISTING_FILES_LIST" | tr '\n' ' ')
fi

if [ -s "$DELETED_FILES_LIST" ]; then
  DELETED_FILES=$(cat "$DELETED_FILES_LIST" | tr '\n' ' ')
  echo "🗑️ Detected deleted files: $DELETED_FILES"
fi

# Clean up temp files
rm -f "$EXISTING_FILES_LIST" "$DELETED_FILES_LIST"

# Trim whitespace
EXISTING_STAGED_FILES=$(echo "$EXISTING_STAGED_FILES" | xargs)
DELETED_FILES=$(echo "$DELETED_FILES" | xargs)

# Detect changes in /apps/ (excluding admin-portal and member-portal)
OTHER_APPS_CHANGES=""
if [ -n "$EXISTING_STAGED_FILES" ]; then
  OTHER_APPS_CHANGES=$(echo "$EXISTING_STAGED_FILES" | tr ' ' '\n' | grep -E "^apps/" | grep -Ev "apps/(admin-portal|member-portal)" || true)
fi

# Determine which projects have changes
ADMIN_PORTAL_CHANGES=""
MEMBER_PORTAL_CHANGES=""
if [ -n "$EXISTING_STAGED_FILES" ]; then
  ADMIN_PORTAL_CHANGES=$(echo "$EXISTING_STAGED_FILES" | tr ' ' '\n' | grep -E "next/apps/admin-portal|next/libs/rxb-ui" || true)
  MEMBER_PORTAL_CHANGES=$(echo "$EXISTING_STAGED_FILES" | tr ' ' '\n' | grep -E "next/apps/member-portal|next/libs/rxb-ui" || true)
fi

# Create a list of projects to lint
PROJECTS_TO_LINT=""

if [ -n "$ADMIN_PORTAL_CHANGES" ]; then
  echo "🔍 Admin portal changes detected"
  PROJECTS_TO_LINT="$PROJECTS_TO_LINT admin-portal"
fi

if [ -n "$MEMBER_PORTAL_CHANGES" ]; then
  echo "🔍 Member portal changes detected"
  PROJECTS_TO_LINT="$PROJECTS_TO_LINT member-portal"
fi

# Trim leading whitespace
PROJECTS_TO_LINT=$(echo "$PROJECTS_TO_LINT" | xargs)

# If there are changes in other /apps/*, lint all projects
if [ -n "$OTHER_APPS_CHANGES" ]; then
  echo "\n📝 Changes detected in other /apps/ projects. Running lint on all projects..."
  if ! run_lint "admin-portal" || ! run_lint "member-portal"; then
    echo "\n❌ Linting failed"
    echo "\n💡 Tip: Click on the file paths above to open them directly in your editor"
    exit 1
  fi
else
  # Run lint on affected projects or all projects if none detected
  if [ -n "$PROJECTS_TO_LINT" ]; then
    echo "\n📝 Running lint on projects: $PROJECTS_TO_LINT"
    
    for PROJECT in $PROJECTS_TO_LINT; do
      echo "\n🔍 Linting $PROJECT..."
      if ! run_lint "$PROJECT"; then
        echo "\n❌ Linting failed for $PROJECT"
        echo "\n💡 Tip: Click on the file paths above to open them directly in your editor"
        exit 1
      fi
    done
  else
    # No specific portal changes detected, lint all projects
    echo "\n📝 No specific portal changes detected, running lint on all projects..."
    if ! run_lint "admin-portal" || ! run_lint "member-portal"; then
      echo "\n❌ Linting failed"
      echo "\n💡 Tip: Click on the file paths above to open them directly in your editor"
      exit 1
    fi
  fi
fi

# Format only existing changed files with nx
echo "\n✨ Formatting existing changed files..."
if [ -n "$EXISTING_STAGED_FILES" ]; then
  # Create a temporary file with only existing files for nx format
  TEMP_FILE_LIST=$(mktemp)
  echo "$EXISTING_STAGED_FILES" | tr ' ' '\n' > "$TEMP_FILE_LIST"
  
  # Run nx format on existing files only
  if [ -s "$TEMP_FILE_LIST" ]; then
    cat "$TEMP_FILE_LIST" | xargs npx nx format:write --files 2>/dev/null || true
  fi
  
  rm -f "$TEMP_FILE_LIST"
fi

# Stage fixed and formatted files (only existing files)
echo "\n📁 Re-staging modified files..."
if [ -n "$EXISTING_STAGED_FILES" ]; then
  echo "$EXISTING_STAGED_FILES" | tr ' ' '\n' | while IFS= read -r file; do
    if [ -n "$file" ] && [ -f "$file" ]; then
      git add "$file"
    fi
  done
fi

# Handle deleted files separately - they should remain staged as deletions
if [ -n "$DELETED_FILES" ]; then
  echo "🗑️ Ensuring deleted files remain staged..."
  # Check if the deletions are still staged, if not re-stage them
  for file in $DELETED_FILES; do
    if [ -n "$file" ]; then
      # Check if file is still staged for deletion
      if git diff --staged --name-only | grep -q "^$file$"; then
        echo "  ✓ $file (still staged for deletion)"
      else
        echo "  ↻ Re-staging deletion: $file"
        git rm "$file" 2>/dev/null || true
      fi
    fi
  done
fi

# Run typecheck on affected projects
if [ -n "$PROJECTS_TO_LINT" ]; then
  echo "\n🔍 Running type checking on projects: $PROJECTS_TO_LINT"
  
  for PROJECT in $PROJECTS_TO_LINT; do
    echo "\n📝 Type checking $PROJECT..."
    if ! npx nx typecheck $PROJECT; then
      echo "\n❌ Type checking failed for $PROJECT"
      exit 1
    fi
  done
else
  echo "\n📝 Running type checking on all projects..."
  if ! npx nx typecheck admin-portal || ! npx nx typecheck member-portal; then
    echo "\n❌ Type checking failed"
    exit 1
  fi
fi

# Now run prettier on all projects (after staging is complete)
echo "\n✨ Running Prettier on all projects..."
echo "  This runs after staging to ensure clean code formatting..."

if [ -n "$OTHER_APPS_CHANGES" ] || [ -z "$PROJECTS_TO_LINT" ]; then
  # Run on all projects
  npx prettier --write "next/apps/admin-portal/**/*.{js,jsx,ts,tsx,css,md,json,scss,html}" --ignore-unknown 2>/dev/null || true
  npx prettier --write "next/apps/member-portal/**/*.{js,jsx,ts,tsx,css,md,json,scss,html}" --ignore-unknown 2>/dev/null || true
else
  # Run on specific projects
  for PROJECT in $PROJECTS_TO_LINT; do
    echo "  ✨ Prettier on $PROJECT..."
    npx prettier --write "next/apps/$PROJECT/**/*.{js,jsx,ts,tsx,css,md,json,scss,html}" --ignore-unknown 2>/dev/null || true
  done
fi

echo "\n✅ Pre-commit checks passed!"