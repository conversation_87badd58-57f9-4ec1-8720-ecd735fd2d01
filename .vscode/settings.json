{"eslint.format.enable": true, "editor.detectIndentation": false, "editor.defaultFormatter": "esbenp.prettier-vscode", "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "typescript.validate.enable": true, "typescript.preferences.includePackageJsonAutoImports": "on", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "jestrunner.jestCommand": "npm run test --", "jestrunner.debugOptions": {"runtimeExecutable": "${workspaceRoot}/node_modules/.bin/react-scripts", "runtimeArgs": ["test", "${fileBasename}", "--runInBand", "--no-cache", "--watchAll=false", "--color"]}, "cSpell.words": ["ADMINPORTALAPIURI", "birthdate", "Chakra", "FAJOBS", "filestorageid", "financials", "HDCR", "<PERSON><PERSON>", "Optum", "OPTUMRX", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Qlik", "templatename", "vals"], "nxConsole.generateAiAgentRules": true}